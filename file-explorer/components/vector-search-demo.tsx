"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Database, FileText, Code, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { SemanticSearchService, CodeContext, SearchSuggestion } from '@/components/background';

interface VectorSearchDemoProps {
  className?: string;
}

export function VectorSearchDemo({ className }: VectorSearchDemoProps) {
  const [searchService, setSearchService] = useState<SemanticSearchService | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isIndexing, setIsIndexing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CodeContext[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Initialize the semantic search service
  useEffect(() => {
    const initializeService = async () => {
      try {
        const service = new SemanticSearchService();
        await service.initialize();
        setSearchService(service);
        setIsInitialized(true);
        
        // Get initial stats
        const initialStats = service.getStats();
        setStats(initialStats);
        
        console.log('Vector search demo initialized');
      } catch (error) {
        console.error('Failed to initialize vector search demo:', error);
        setError('Failed to initialize semantic search service');
      }
    };

    initializeService();
  }, []);

  // Index sample project
  const handleIndexProject = async () => {
    if (!searchService) return;

    setIsIndexing(true);
    setError(null);

    try {
      await searchService.indexProject('/sample-project');
      const newStats = searchService.getStats();
      setStats(newStats);
      console.log('Project indexed successfully');
    } catch (error) {
      console.error('Failed to index project:', error);
      setError('Failed to index project');
    } finally {
      setIsIndexing(false);
    }
  };

  // Perform semantic search
  const handleSearch = async () => {
    if (!searchService || !searchQuery.trim()) return;

    setIsSearching(true);
    setError(null);

    try {
      const results = await searchService.searchCode({
        query: searchQuery,
        maxResults: 10,
        minSimilarity: 0.1
      });
      
      setSearchResults(results);
      console.log(`Found ${results.length} results for: "${searchQuery}"`);
    } catch (error) {
      console.error('Search failed:', error);
      setError('Search failed');
    } finally {
      setIsSearching(false);
    }
  };

  // Get search suggestions
  const handleGetSuggestions = async () => {
    if (!searchService) return;

    try {
      const suggestions = await searchService.getSearchSuggestions(searchQuery);
      setSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to get suggestions:', error);
    }
  };

  // Clear index
  const handleClearIndex = async () => {
    if (!searchService) return;

    try {
      await searchService.clearIndex();
      setSearchResults([]);
      setSuggestions([]);
      setStats(searchService.getStats());
      console.log('Index cleared');
    } catch (error) {
      console.error('Failed to clear index:', error);
      setError('Failed to clear index');
    }
  };

  const formatSimilarity = (similarity: number) => {
    return `${(similarity * 100).toFixed(1)}%`;
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      typescript: 'bg-blue-100 text-blue-800',
      javascript: 'bg-yellow-100 text-yellow-800',
      python: 'bg-green-100 text-green-800',
      java: 'bg-red-100 text-red-800',
      markdown: 'bg-gray-100 text-gray-800',
      json: 'bg-purple-100 text-purple-800'
    };
    return colors[language] || 'bg-gray-100 text-gray-800';
  };

  if (!isInitialized) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Initializing Vector Search...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Vector Database Demo</span>
          </CardTitle>
          <CardDescription>
            Semantic code search using vector embeddings and similarity matching
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <Button 
              onClick={handleIndexProject} 
              disabled={isIndexing}
              variant="outline"
            >
              {isIndexing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Indexing...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Index Sample Project
                </>
              )}
            </Button>
            
            <Button 
              onClick={handleClearIndex} 
              variant="outline"
              disabled={!stats || stats.totalDocuments === 0}
            >
              Clear Index
            </Button>

            {stats && (
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span>{stats.totalDocuments} documents</span>
                <span>{stats.languages.length} languages</span>
                <span>{(stats.totalSize / 1024).toFixed(1)}KB indexed</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Semantic Search</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="Search for code patterns, functions, or concepts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={isSearching || !searchQuery.trim()}
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            <Button 
              onClick={handleGetSuggestions} 
              variant="outline"
              disabled={!searchQuery.trim()}
            >
              Suggestions
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}

          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Suggestions:</h4>
              <div className="flex flex-wrap gap-2">
                {suggestions.map((suggestion, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setSearchQuery(suggestion.query)}
                  >
                    {suggestion.query}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-5 w-5" />
              <span>Search Results ({searchResults.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {searchResults.map((result, index) => (
                  <div key={result.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge className={getLanguageColor(result.language)}>
                          {result.language}
                        </Badge>
                        <span className="text-sm font-medium">{result.filePath}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {formatSimilarity(result.similarity)}
                        </Badge>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </div>
                    </div>
                    
                    <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
                      <code>{result.content}</code>
                    </pre>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Size: {result.metadata.fileSize} bytes</span>
                      <span>Relevance: {(result.relevanceScore * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {searchQuery && searchResults.length === 0 && !isSearching && (
        <Card>
          <CardContent className="text-center py-8">
            <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Results Found</h3>
            <p className="text-muted-foreground">
              Try different keywords or index a project first
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
