// components/agents/model-registry-service.ts
import { LL<PERSON>rovider, getProviderConfig } from './llm-provider-registry';
import { getModelMetadata, getModelsByProvider, ModelMetadata } from './model-metadata';
import { fetchModelsForProvider, FetchedModel } from './fetch-models';

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
  owned_by?: string;
  created?: number;
  metadata?: ModelMetadata;
}

export interface ModelCacheEntry {
  models: ModelInfo[];
  timestamp: number;
  ttl: number;
}

export class ModelRegistryService {
  private static instance: ModelRegistryService;
  private modelCache: Map<LLMProvider, ModelCacheEntry> = new Map();
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_RETRIES = 3;
  private refreshIntervals: Map<LLMProvider, NodeJS.Timeout> = new Map();
  private readonly AUTO_REFRESH_INTERVAL = 60 * 60 * 1000; // 1 hour

  private constructor() {}

  public static getInstance(): ModelRegistryService {
    if (!ModelRegistryService.instance) {
      ModelRegistryService.instance = new ModelRegistryService();
    }
    return ModelRegistryService.instance;
  }

  /**
   * Fetch models for a specific provider with caching
   */
  public async fetchModels(provider: LLMProvider, apiKey: string, forceRefresh = false): Promise<string[]> {
    try {
      // Check cache first
      if (!forceRefresh && this.isCacheValid(provider)) {
        const cached = this.modelCache.get(provider);
        if (cached) {
          console.log(`ModelRegistryService: Using cached models for ${provider}`);
          return cached.models.map(m => m.id);
        }
      }

      const config = getProviderConfig(provider);

      if (!config.supportsModelFetching) {
        console.log(`ModelRegistryService: ${provider} doesn't support model fetching, using static list`);
        return this.getStaticModels(provider);
      }

      // Check if Electron API is available
      if (!this.isElectronAPIAvailable()) {
        console.log(`ModelRegistryService: Electron API not available, falling back to static models for ${provider}`);
        return this.getStaticModels(provider);
      }

      console.log(`ModelRegistryService: Fetching models for ${provider} via Electron API...`);

      // Try enhanced metadata fetching first, fallback to basic fetching
      let models: ModelInfo[] = [];
      try {
        if (this.isEnhancedAPIAvailable()) {
          const rawModels = await window.electronAPI.llm.fetchModelsWithMetadata(provider, apiKey);
          models = this.enrichModelsFromAPI(provider, rawModels);
        } else {
          const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);
          models = this.enrichModelsFromIds(provider, modelIds);
        }
      } catch (enhancedError) {
        console.log(`ModelRegistryService: Enhanced fetching failed, falling back to basic fetch for ${provider}`);
        const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);
        models = this.enrichModelsFromIds(provider, modelIds);
      }

      // Cache the results
      this.cacheModels(provider, models);

      return models.map(m => m.id);

    } catch (error) {
      console.error(`ModelRegistryService: Failed to fetch models for ${provider}:`, error);

      // Return cached models if available, otherwise static models
      const cached = this.modelCache.get(provider);
      if (cached) {
        console.log(`ModelRegistryService: Using stale cache for ${provider} due to error`);
        return cached.models.map(m => m.id);
      }

      console.log(`ModelRegistryService: Falling back to static models for ${provider}`);
      return this.getStaticModels(provider);
    }
  }

  /**
   * Check if Electron API is available for model fetching
   */
  private isElectronAPIAvailable(): boolean {
    return typeof window !== 'undefined' &&
           window.electronAPI?.llm?.fetchModels !== undefined;
  }

  /**
   * Check if enhanced metadata API is available
   */
  private isEnhancedAPIAvailable(): boolean {
    return typeof window !== 'undefined' &&
           window.electronAPI?.llm?.fetchModelsWithMetadata !== undefined;
  }

  /**
   * Enrich models from API response with metadata
   */
  private enrichModelsFromAPI(provider: LLMProvider, rawModels: any[]): ModelInfo[] {
    const config = getProviderConfig(provider);

    return rawModels.map(rawModel => {
      const id = rawModel.id || rawModel.name;
      const metadata = getModelMetadata(provider, id);

      return {
        id,
        name: metadata?.label || rawModel.name || id,
        description: metadata?.notes || rawModel.description || `${config.name} model: ${id}`,
        contextLength: metadata?.contextSize || rawModel.inputTokenLimit || rawModel.context_length,
        pricing: metadata?.pricing || this.extractPricingFromAPI(rawModel),
        owned_by: rawModel.owned_by,
        created: rawModel.created,
        metadata
      };
    });
  }

  /**
   * Enrich models from model IDs with metadata
   */
  private enrichModelsFromIds(provider: LLMProvider, modelIds: string[]): ModelInfo[] {
    const config = getProviderConfig(provider);

    return modelIds.map(id => {
      const metadata = getModelMetadata(provider, id);
      return {
        id,
        name: metadata?.label || id,
        description: metadata?.notes || `${config.name} model: ${id}`,
        contextLength: metadata?.contextSize,
        pricing: metadata?.pricing,
        metadata
      };
    });
  }

  /**
   * Extract pricing information from API response
   */
  private extractPricingFromAPI(rawModel: any): { input: number; output: number } | undefined {
    // OpenRouter provides pricing information
    if (rawModel.pricing) {
      return {
        input: rawModel.pricing.prompt || 0,
        output: rawModel.pricing.completion || 0
      };
    }

    // Some providers may include cost information
    if (rawModel.cost_per_token) {
      return {
        input: rawModel.cost_per_token.input || 0,
        output: rawModel.cost_per_token.output || 0
      };
    }

    return undefined;
  }

  /**
   * Get static models for providers that don't support dynamic fetching
   */
  private getStaticModels(provider: LLMProvider): string[] {
    const config = getProviderConfig(provider);
    const staticModelIds = Object.keys(config.modelMap);

    // For providers without dynamic fetching, also include models from metadata
    const metadataModels = getModelsByProvider(provider);
    const metadataModelIds = metadataModels.map(m => m.modelId);

    // Combine and deduplicate
    const allModelIds = [...new Set([...staticModelIds, ...metadataModelIds])];

    console.log(`ModelRegistryService: Using ${allModelIds.length} static models for ${provider}`);
    return allModelIds;
  }

  /**
   * Cache models for a provider
   */
  private cacheModels(provider: LLMProvider, models: ModelInfo[]): void {
    this.modelCache.set(provider, {
      models,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    });
    console.log(`ModelRegistryService: Cached ${models.length} models for ${provider}`);
  }

  /**
   * Check if cache is valid for a provider
   */
  private isCacheValid(provider: LLMProvider): boolean {
    const cached = this.modelCache.get(provider);
    if (!cached) return false;

    const age = Date.now() - cached.timestamp;
    return age < cached.ttl;
  }

  /**
   * Clear cache for a specific provider or all providers
   */
  public clearCache(provider?: LLMProvider): void {
    if (provider) {
      this.modelCache.delete(provider);
      console.log(`ModelRegistryService: Cleared cache for ${provider}`);
    } else {
      this.modelCache.clear();
      console.log('ModelRegistryService: Cleared all model cache');
    }
  }

  /**
   * Get cached models without fetching
   */
  public getCachedModels(provider: LLMProvider): string[] | null {
    const cached = this.modelCache.get(provider);
    if (cached && this.isCacheValid(provider)) {
      return cached.models.map(m => m.id);
    }
    return null;
  }

  /**
   * Get enriched model information with metadata
   */
  public getEnrichedModels(provider: LLMProvider): ModelInfo[] {
    const modelIds = this.getStaticModels(provider);
    return modelIds.map(id => {
      const metadata = getModelMetadata(provider, id);
      const config = getProviderConfig(provider);
      return {
        id,
        name: metadata?.label || id,
        description: metadata?.notes || `${config.name} model: ${id}`,
        contextLength: metadata?.contextSize,
        pricing: metadata?.pricing,
        metadata
      };
    });
  }

  /**
   * Get comprehensive model information with live fetching and metadata
   */
  public async getComprehensiveModels(provider: LLMProvider, apiKey: string, forceRefresh = false): Promise<ModelInfo[]> {
    try {
      // Check cache first
      if (!forceRefresh && this.isCacheValid(provider)) {
        const cached = this.modelCache.get(provider);
        if (cached) {
          console.log(`ModelRegistryService: Using cached comprehensive models for ${provider}`);
          return cached.models;
        }
      }

      const config = getProviderConfig(provider);

      if (!config.supportsModelFetching) {
        console.log(`ModelRegistryService: ${provider} doesn't support model fetching, using enriched static list`);
        return this.getEnrichedModels(provider);
      }

      // Check if Electron API is available
      if (!this.isElectronAPIAvailable()) {
        console.log(`ModelRegistryService: Electron API not available, falling back to enriched static models for ${provider}`);
        return this.getEnrichedModels(provider);
      }

      console.log(`ModelRegistryService: Fetching comprehensive models for ${provider} via Electron API...`);

      // Try enhanced metadata fetching first
      let models: ModelInfo[] = [];
      try {
        if (this.isEnhancedAPIAvailable()) {
          const rawModels = await window.electronAPI.llm.fetchModelsWithMetadata(provider, apiKey);
          models = this.enrichModelsFromAPI(provider, rawModels);
        } else {
          const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);
          models = this.enrichModelsFromIds(provider, modelIds);
        }
      } catch (enhancedError) {
        console.log(`ModelRegistryService: Enhanced fetching failed, falling back to basic fetch for ${provider}`);
        const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);
        models = this.enrichModelsFromIds(provider, modelIds);
      }

      // Cache the results
      this.cacheModels(provider, models);

      return models;

    } catch (error) {
      console.error(`ModelRegistryService: Failed to fetch comprehensive models for ${provider}:`, error);

      // Return cached models if available, otherwise enriched static models
      const cached = this.modelCache.get(provider);
      if (cached) {
        console.log(`ModelRegistryService: Using stale cache for comprehensive models for ${provider} due to error`);
        return cached.models;
      }

      console.log(`ModelRegistryService: Falling back to enriched static models for ${provider}`);
      return this.getEnrichedModels(provider);
    }
  }

  /**
   * Schedule automatic refresh for a provider
   */
  public scheduleAutoRefresh(provider: LLMProvider, apiKey: string): void {
    // Clear existing interval if any
    this.clearAutoRefresh(provider);

    const config = getProviderConfig(provider);
    if (!config.supportsModelFetching) {
      console.log(`ModelRegistryService: Skipping auto-refresh for ${provider} (no dynamic fetching)`);
      return;
    }

    const interval = setInterval(async () => {
      try {
        console.log(`ModelRegistryService: Auto-refreshing models for ${provider}`);
        await this.getComprehensiveModels(provider, apiKey, true);
      } catch (error) {
        console.error(`ModelRegistryService: Auto-refresh failed for ${provider}:`, error);
      }
    }, this.AUTO_REFRESH_INTERVAL);

    this.refreshIntervals.set(provider, interval);
    console.log(`ModelRegistryService: Scheduled auto-refresh for ${provider} every ${this.AUTO_REFRESH_INTERVAL / 60000} minutes`);
  }

  /**
   * Clear automatic refresh for a provider
   */
  public clearAutoRefresh(provider: LLMProvider): void {
    const interval = this.refreshIntervals.get(provider);
    if (interval) {
      clearInterval(interval);
      this.refreshIntervals.delete(provider);
      console.log(`ModelRegistryService: Cleared auto-refresh for ${provider}`);
    }
  }

  /**
   * Clear all automatic refreshes
   */
  public clearAllAutoRefresh(): void {
    for (const [provider, interval] of this.refreshIntervals.entries()) {
      clearInterval(interval);
    }
    this.refreshIntervals.clear();
    console.log('ModelRegistryService: Cleared all auto-refresh intervals');
  }

  /**
   * Get cache statistics with refresh status
   */
  public getCacheStats(): Record<LLMProvider, { count: number; age: number; valid: boolean; autoRefresh: boolean }> {
    const stats: any = {};

    for (const [provider, cache] of this.modelCache.entries()) {
      const age = Date.now() - cache.timestamp;
      stats[provider] = {
        count: cache.models.length,
        age: Math.round(age / 1000), // in seconds
        valid: age < cache.ttl,
        autoRefresh: this.refreshIntervals.has(provider)
      };
    }

    return stats;
  }

  /**
   * Warm up cache for all providers with API keys
   */
  public async warmUpCache(apiKeys: Record<string, string>): Promise<void> {
    console.log('ModelRegistryService: Warming up cache for all providers...');

    const warmupPromises = Object.entries(apiKeys).map(async ([provider, apiKey]) => {
      if (apiKey && apiKey.trim()) {
        try {
          await this.getComprehensiveModels(provider as LLMProvider, apiKey);
          console.log(`ModelRegistryService: Cache warmed up for ${provider}`);
        } catch (error) {
          console.error(`ModelRegistryService: Cache warmup failed for ${provider}:`, error);
        }
      }
    });

    await Promise.allSettled(warmupPromises);
    console.log('ModelRegistryService: Cache warmup completed');
  }

  /**
   * Get comprehensive cache information
   */
  public getCacheInfo(): {
    totalProviders: number;
    cachedProviders: number;
    totalModels: number;
    oldestCache: number;
    newestCache: number;
  } {
    const caches = Array.from(this.modelCache.values());
    const now = Date.now();

    return {
      totalProviders: this.modelCache.size,
      cachedProviders: caches.filter(cache => (now - cache.timestamp) < cache.ttl).length,
      totalModels: caches.reduce((sum, cache) => sum + cache.models.length, 0),
      oldestCache: caches.length > 0 ? Math.min(...caches.map(cache => now - cache.timestamp)) : 0,
      newestCache: caches.length > 0 ? Math.max(...caches.map(cache => now - cache.timestamp)) : 0
    };
  }

  /**
   * Clean model fetching using dedicated fetch-models service
   */
  public async fetchModelsClean(provider: LLMProvider, apiKey: string, forceRefresh = false): Promise<string[]> {
    try {
      // Check cache first
      if (!forceRefresh && this.isCacheValid(provider)) {
        const cached = this.modelCache.get(provider);
        if (cached) {
          console.log(`ModelRegistryService: Using cached models for ${provider}`);
          return cached.models.map(m => m.id);
        }
      }

      console.log(`ModelRegistryService: Fetching models for ${provider} using clean fetcher...`);

      // Use clean fetching logic
      const fetchedModels = await fetchModelsForProvider(provider, apiKey);

      // Convert to ModelInfo format
      const models: ModelInfo[] = fetchedModels.map(model => ({
        id: model.id,
        name: model.name,
        description: model.metadata?.notes || `${provider} model: ${model.id}`,
        contextLength: model.contextLength,
        pricing: model.pricing,
        metadata: model.metadata
      }));

      // Cache the results
      this.cacheModels(provider, models);

      console.log(`ModelRegistryService: Successfully fetched ${models.length} models for ${provider}`);
      return models.map(m => m.id);

    } catch (error) {
      console.error(`ModelRegistryService: Clean fetch failed for ${provider}:`, error);

      // Return cached models if available, otherwise static models
      const cached = this.modelCache.get(provider);
      if (cached) {
        console.log(`ModelRegistryService: Using stale cache for ${provider} due to error`);
        return cached.models.map(m => m.id);
      }

      console.log(`ModelRegistryService: Falling back to static models for ${provider}`);
      return this.getStaticModels(provider);
    }
  }
}
