// components/agents/llm-provider-registry.ts
import { ANTHROPIC_MODEL_MAP } from './anthropic-models';

export type LLMProvider = 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks';

export interface ProviderConfig {
  name: string;
  apiUrl: string;
  modelMap: Record<string, string>;
  headers: (apiKey: string) => Record<string, string>;
  requestFormat: 'openai' | 'anthropic' | 'custom';
  responseFormat: 'openai' | 'anthropic' | 'custom';
  maxTokensSupported: number;
  costPer1kTokens: {
    input: number;
    output: number;
  };
  documentationUrl: string;
  keyValidationEndpoint?: string;
  modelsEndpoint?: string;
  supportsModelFetching: boolean;
}

export const LLMProviderRegistry: Record<LLMProvider, ProviderConfig> = {
  openai: {
    name: 'OpenAI',
    apiUrl: 'https://api.openai.com/v1/chat/completions',
    modelMap: {
      'gpt-4': 'gpt-4',
      'gpt-4-turbo': 'gpt-4-turbo',
      'gpt-4o': 'gpt-4o',
      'gpt-4o-mini': 'gpt-4o-mini',
      'gpt-3.5-turbo': 'gpt-3.5-turbo'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    maxTokensSupported: 128000,
    costPer1kTokens: {
      input: 0.03,
      output: 0.06
    },
    documentationUrl: 'https://platform.openai.com/api-keys',
    keyValidationEndpoint: 'https://api.openai.com/v1/models',
    modelsEndpoint: 'https://api.openai.com/v1/models',
    supportsModelFetching: true
  },

  anthropic: {
    name: 'Anthropic',
    apiUrl: 'https://api.anthropic.com/v1/messages',
    modelMap: ANTHROPIC_MODEL_MAP,
    headers: (apiKey: string) => ({
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01',
      'Content-Type': 'application/json'
    }),
    requestFormat: 'anthropic',
    responseFormat: 'anthropic',
    maxTokensSupported: 200000,
    costPer1kTokens: {
      input: 0.015,
      output: 0.075
    },
    documentationUrl: 'https://console.anthropic.com/settings/keys',
    keyValidationEndpoint: 'https://api.anthropic.com/v1/messages',
    supportsModelFetching: false // Anthropic doesn't have a public models API yet
  },

  openrouter: {
    name: 'OpenRouter',
    apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
    modelMap: {
      'meta-llama/llama-3.1-405b-instruct': 'meta-llama/llama-3.1-405b-instruct',
      'meta-llama/llama-3.1-70b-instruct': 'meta-llama/llama-3.1-70b-instruct',
      'mistralai/mixtral-8x7b-instruct': 'mistralai/mixtral-8x7b-instruct',
      'anthropic/claude-3-sonnet': 'anthropic/claude-3-sonnet',
      'openai/gpt-4o': 'openai/gpt-4o',
      'google/gemini-pro': 'google/gemini-pro',
      'cohere/command-r-plus': 'cohere/command-r-plus'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://synapse.dev',
      'X-Title': 'Synapse Agent System'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    maxTokensSupported: 128000,
    costPer1kTokens: {
      input: 0.002,
      output: 0.006
    },
    documentationUrl: 'https://openrouter.ai/keys',
    keyValidationEndpoint: 'https://openrouter.ai/api/v1/models',
    modelsEndpoint: 'https://openrouter.ai/api/v1/models',
    supportsModelFetching: true
  },

  azure: {
    name: 'Azure OpenAI',
    apiUrl: 'https://{resource}.openai.azure.com/openai/deployments/{deployment}/chat/completions?api-version=2024-02-15-preview',
    modelMap: {
      'gpt-4': 'gpt-4',
      'gpt-35-turbo': 'gpt-35-turbo',
      'gpt-4-turbo': 'gpt-4-turbo'
    },
    headers: (apiKey: string) => ({
      'api-key': apiKey,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    maxTokensSupported: 128000,
    costPer1kTokens: {
      input: 0.03,
      output: 0.06
    },
    documentationUrl: 'https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/OpenAI',
    keyValidationEndpoint: undefined, // Requires custom endpoint
    supportsModelFetching: false // Azure models are deployment-specific
  },

  google: {
    name: 'Google AI',
    apiUrl: 'https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent',
    modelMap: {
      'gemini-pro': 'gemini-pro',
      'gemini-1.5-pro': 'gemini-1.5-pro',
      'gemini-1.5-flash': 'gemini-1.5-flash'
    },
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json'
    }),
    requestFormat: 'custom',
    responseFormat: 'custom',
    maxTokensSupported: 1000000,
    costPer1kTokens: {
      input: 0.00125,
      output: 0.00375
    },
    documentationUrl: 'https://makersuite.google.com/app/apikey',
    keyValidationEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
    modelsEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
    supportsModelFetching: true
  },

  deepseek: {
    name: 'DeepSeek',
    apiUrl: 'https://api.deepseek.com/chat/completions',
    modelMap: {
      'deepseek-chat': 'deepseek-chat',
      'deepseek-coder': 'deepseek-coder'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    maxTokensSupported: 32000,
    costPer1kTokens: {
      input: 0.0014,
      output: 0.0028
    },
    documentationUrl: 'https://platform.deepseek.com/api_keys',
    keyValidationEndpoint: 'https://api.deepseek.com/models',
    modelsEndpoint: 'https://api.deepseek.com/models',
    supportsModelFetching: true
  },

  fireworks: {
    name: 'Fireworks AI',
    apiUrl: 'https://api.fireworks.ai/inference/v1/chat/completions',
    modelMap: {
      'llama-3.1-70b': 'accounts/fireworks/models/llama-v3p1-70b-instruct',
      'mixtral-8x7b': 'accounts/fireworks/models/mixtral-8x7b-instruct',
      'qwen-72b': 'accounts/fireworks/models/qwen2-72b-instruct'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    maxTokensSupported: 32000,
    costPer1kTokens: {
      input: 0.0009,
      output: 0.0009
    },
    documentationUrl: 'https://fireworks.ai/api-keys',
    keyValidationEndpoint: 'https://api.fireworks.ai/inference/v1/models',
    modelsEndpoint: 'https://api.fireworks.ai/inference/v1/models',
    supportsModelFetching: true
  }
};

export function getProviderConfig(provider: LLMProvider): ProviderConfig {
  return LLMProviderRegistry[provider];
}

export function getAllProviders(): LLMProvider[] {
  return Object.keys(LLMProviderRegistry) as LLMProvider[];
}

export function getProviderModels(provider: LLMProvider): string[] {
  return Object.keys(LLMProviderRegistry[provider].modelMap);
}

export function validateProviderModel(provider: LLMProvider, model: string): boolean {
  return model in LLMProviderRegistry[provider].modelMap;
}

export function getProviderModelId(provider: LLMProvider, model: string): string {
  const config = LLMProviderRegistry[provider];
  return config.modelMap[model] || model;
}

export function estimateCost(provider: LLMProvider, inputTokens: number, outputTokens: number): number {
  const config = LLMProviderRegistry[provider];
  const inputCost = (inputTokens / 1000) * config.costPer1kTokens.input;
  const outputCost = (outputTokens / 1000) * config.costPer1kTokens.output;
  return inputCost + outputCost;
}
