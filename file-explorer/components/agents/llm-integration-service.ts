// components/agents/llm-integration-service.ts
import { LLMRequestService } from './llm-request-service';
import { SettingsManager } from '../settings/settings-manager';
import { getAllProviders, LLMProvider } from './llm-provider-registry';

export class LLMIntegrationService {
  private static instance: LLMIntegrationService;
  private llmService: LLMRequestService;
  private settingsManager: SettingsManager;
  private initialized = false;

  private constructor() {
    this.llmService = LLMRequestService.getInstance();
    this.settingsManager = new SettingsManager();
  }

  public static getInstance(): LLMIntegrationService {
    if (!LLMIntegrationService.instance) {
      LLMIntegrationService.instance = new LLMIntegrationService();
    }
    return LLMIntegrationService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load API keys from settings and set them in LLM service
      const settings = this.settingsManager.getSettings();
      
      getAllProviders().forEach(provider => {
        const apiKey = settings.apiKeys[provider];
        if (apiKey) {
          this.llmService.setApiKey(provider, apiKey);
          console.log(`LLMIntegrationService: API key loaded for ${provider}`);
        }
      });

      // Listen for settings changes to update API keys
      this.settingsManager.onSettingsChange((newSettings) => {
        this.updateApiKeys(newSettings.apiKeys);
      });

      this.initialized = true;
      console.log('LLMIntegrationService: Initialized successfully');
    } catch (error) {
      console.error('LLMIntegrationService: Failed to initialize:', error);
      throw error;
    }
  }

  private updateApiKeys(apiKeys: Record<string, string>): void {
    getAllProviders().forEach(provider => {
      const apiKey = apiKeys[provider];
      if (apiKey) {
        this.llmService.setApiKey(provider, apiKey);
        console.log(`LLMIntegrationService: API key updated for ${provider}`);
      }
    });
  }

  public getLLMService(): LLMRequestService {
    return this.llmService;
  }

  public getSettingsManager(): SettingsManager {
    return this.settingsManager;
  }

  public async validateAllApiKeys(): Promise<Record<LLMProvider, boolean>> {
    const results: Record<LLMProvider, boolean> = {} as Record<LLMProvider, boolean>;
    const settings = this.settingsManager.getSettings();

    for (const provider of getAllProviders()) {
      const apiKey = settings.apiKeys[provider];
      if (apiKey) {
        try {
          results[provider] = await this.llmService.validateApiKey(provider, apiKey);
        } catch (error) {
          console.error(`Failed to validate API key for ${provider}:`, error);
          results[provider] = false;
        }
      } else {
        results[provider] = false;
      }
    }

    return results;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Export singleton instance
export const llmIntegration = LLMIntegrationService.getInstance();
