// components/agents/fetch-models.ts
// Clean dynamic model fetching for all supported providers

import { ModelMetadata, getModelMetadata } from './model-metadata';
import { LL<PERSON>rovider } from './llm-provider-registry';

export interface FetchedModel {
  id: string;
  name: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
  capabilities?: string[];
  metadata?: ModelMetadata;
}

/**
 * Fetch OpenAI models dynamically
 */
export async function fetchOpenAIModels(apiKey: string): Promise<FetchedModel[]> {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: { 
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Filter for chat/completion models only
    const chatModels = data.data.filter((model: any) =>
      model.id.startsWith("gpt-") && 
      !model.id.includes("test") &&
      !model.id.includes("embedding") &&
      !model.id.includes("whisper") &&
      !model.id.includes("tts") &&
      !model.id.includes("dall-e")
    );

    return chatModels.map((model: any) => {
      const metadata = getModelMetadata('openai', model.id);
      return {
        id: model.id,
        name: metadata?.label || model.id.toUpperCase(),
        contextLength: metadata?.contextSize,
        pricing: metadata?.pricing,
        capabilities: metadata?.tags,
        metadata
      };
    });

  } catch (error) {
    console.error('Failed to fetch OpenAI models:', error);
    throw error;
  }
}

/**
 * Fetch OpenRouter models dynamically
 */
export async function fetchOpenRouterModels(apiKey: string): Promise<FetchedModel[]> {
  try {
    const response = await fetch('https://openrouter.ai/api/v1/models', {
      headers: { 
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Filter for trusted providers and public models
    const trustedProviders = [
      'meta-llama', 'mistralai', 'anthropic', 'openai', 'google', 
      'cohere', 'microsoft', 'huggingfaceh4', 'nousresearch'
    ];
    
    const publicModels = data.data.filter((model: any) => {
      const providerId = model.id.split('/')[0];
      return trustedProviders.includes(providerId) && 
             !model.id.includes('test') &&
             !model.id.includes('preview');
    });

    return publicModels.map((model: any) => {
      const metadata = getModelMetadata('openrouter', model.id);
      return {
        id: model.id,
        name: metadata?.label || model.name || model.id,
        contextLength: metadata?.contextSize || model.context_length,
        pricing: metadata?.pricing || (model.pricing ? {
          input: model.pricing.prompt || 0,
          output: model.pricing.completion || 0
        } : undefined),
        capabilities: metadata?.tags,
        metadata
      };
    });

  } catch (error) {
    console.error('Failed to fetch OpenRouter models:', error);
    throw error;
  }
}

/**
 * Get static Google Gemini models (no public API)
 */
export function getGoogleGeminiModels(): FetchedModel[] {
  const geminiModels = [
    'gemini-1.5-pro',
    'gemini-1.5-flash', 
    'gemini-1.0-pro'
  ];

  return geminiModels.map(modelId => {
    const metadata = getModelMetadata('google', modelId);
    return {
      id: modelId,
      name: metadata?.label || modelId,
      contextLength: metadata?.contextSize,
      pricing: metadata?.pricing,
      capabilities: metadata?.tags,
      metadata
    };
  });
}

/**
 * Get static Cohere models (no public API)
 */
export function getCohereModels(): FetchedModel[] {
  const cohereModels = [
    'command-r-plus',
    'command-r',
    'command',
    'command-light'
  ];

  return cohereModels.map(modelId => {
    const metadata = getModelMetadata('cohere', modelId);
    return {
      id: modelId,
      name: metadata?.label || modelId,
      contextLength: metadata?.contextSize,
      pricing: metadata?.pricing,
      capabilities: metadata?.tags,
      metadata
    };
  });
}

/**
 * Get static Mistral models (fallback if not via OpenRouter)
 */
export function getMistralModels(): FetchedModel[] {
  const mistralModels = [
    'mistral-large-latest',
    'mistral-medium-latest',
    'mistral-small-latest',
    'mixtral-8x7b-instruct',
    'mixtral-8x22b-instruct'
  ];

  return mistralModels.map(modelId => {
    const metadata = getModelMetadata('mistral', modelId);
    return {
      id: modelId,
      name: metadata?.label || modelId,
      contextLength: metadata?.contextSize,
      pricing: metadata?.pricing,
      capabilities: metadata?.tags,
      metadata
    };
  });
}

/**
 * Fetch models for any provider with fallback to static
 */
export async function fetchModelsForProvider(
  provider: LLMProvider, 
  apiKey: string
): Promise<FetchedModel[]> {
  try {
    switch (provider) {
      case 'openai':
        return await fetchOpenAIModels(apiKey);
      
      case 'openrouter':
        return await fetchOpenRouterModels(apiKey);
      
      case 'google':
        return getGoogleGeminiModels();
      
      case 'cohere':
        return getCohereModels();
      
      case 'mistral':
        return getMistralModels();
      
      case 'anthropic':
        // Anthropic is handled separately with existing logic
        throw new Error('Anthropic models should be fetched via existing service');
      
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  } catch (error) {
    console.error(`Failed to fetch models for ${provider}:`, error);
    
    // Fallback to static models from metadata
    const staticModels = getStaticModelsForProvider(provider);
    console.log(`Using ${staticModels.length} static models for ${provider}`);
    return staticModels;
  }
}

/**
 * Get static models from metadata as fallback
 */
function getStaticModelsForProvider(provider: LLMProvider): FetchedModel[] {
  // This would use the existing model metadata
  const { getModelsByProvider } = require('./model-metadata');
  const models = getModelsByProvider(provider);
  
  return models.map((metadata: ModelMetadata) => ({
    id: metadata.modelId,
    name: metadata.label,
    contextLength: metadata.contextSize,
    pricing: metadata.pricing,
    capabilities: metadata.tags,
    metadata
  }));
}

/**
 * Validate if a model supports chat/completion
 */
export async function validateModelForChat(
  provider: LLMProvider,
  modelId: string,
  apiKey: string
): Promise<boolean> {
  if (provider !== 'openai') {
    // For non-OpenAI providers, assume valid if in our list
    return true;
  }

  try {
    // Test with a minimal completion request
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: modelId,
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1
      })
    });

    return response.ok || response.status === 400; // 400 is ok, means model exists
  } catch (error) {
    console.error(`Model validation failed for ${modelId}:`, error);
    return false;
  }
}
