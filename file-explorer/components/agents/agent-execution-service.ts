// components/agents/agent-execution-service.ts
import { MonacoIntegrationManager } from '../background/monaco-integration';
import { FileOperationsManager } from '../background/file-operations';
import { TerminalIntegrationManager } from '../background/terminal-integration';
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { AgentContext } from './agent-base';

export interface ExecutionResult {
  success: boolean;
  output: string;
  files?: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }>;
  terminalOutput?: string;
  kanbanUpdates?: Array<{ cardId: string; action: string; result: any }>;
  error?: string;
  metadata?: Record<string, any>;
}

export interface FileCreationRequest {
  path: string;
  content: string;
  language?: string;
  openInEditor?: boolean;
}

export interface TerminalCommandRequest {
  command: string;
  workingDirectory?: string;
  timeout?: number;
  category: 'build' | 'test' | 'install' | 'git' | 'utility';
}

export interface KanbanUpdateRequest {
  cardId?: string;
  action: 'create' | 'update' | 'move' | 'complete';
  data: any;
}

export class AgentExecutionService {
  private static instance: AgentExecutionService;
  private monacoManager: MonacoIntegrationManager;
  private fileManager: FileOperationsManager;
  private terminalManager: TerminalIntegrationManager;

  constructor() {
    this.monacoManager = MonacoIntegrationManager.getInstance();
    this.fileManager = FileOperationsManager.getInstance();
    this.terminalManager = TerminalIntegrationManager.getInstance();
  }

  public static getInstance(): AgentExecutionService {
    if (!AgentExecutionService.instance) {
      AgentExecutionService.instance = new AgentExecutionService();
    }
    return AgentExecutionService.instance;
  }

  /**
   * Create files and optionally open them in Monaco editor
   */
  async createFiles(files: FileCreationRequest[], agentId: string): Promise<ExecutionResult> {
    const results: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Creating ${files.length} files for agent ${agentId}`);

    for (const file of files) {
      try {
        // Create the file
        const result = await this.fileManager.createFile(file.path, file.content, {
          encoding: 'utf8',
          createDirectories: true
        }, agentId);

        if (result.success) {
          results.push({
            path: file.path,
            content: file.content,
            action: 'created'
          });

          // Open in Monaco editor if requested
          if (file.openInEditor) {
            try {
              // Note: In a real implementation, we'd need to trigger the editor to open this file
              // For now, we'll log the intent
              console.log(`AgentExecutionService: File ${file.path} marked for editor opening`);
            } catch (editorError) {
              console.warn(`Failed to open ${file.path} in editor:`, editorError);
            }
          }

          console.log(`AgentExecutionService: Created file ${file.path} (${file.content.length} chars)`);
        } else {
          errors.push(`Failed to create ${file.path}: ${result.error}`);
        }
      } catch (error) {
        const errorMsg = `Error creating ${file.path}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      output: errors.length === 0 
        ? `Successfully created ${results.length} files`
        : `Created ${results.length} files with ${errors.length} errors`,
      files: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        filesCreated: results.length,
        errors: errors.length,
        agentId
      }
    };
  }

  /**
   * Execute terminal commands
   */
  async executeTerminalCommands(commands: TerminalCommandRequest[], agentId: string): Promise<ExecutionResult> {
    const outputs: string[] = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Executing ${commands.length} terminal commands for agent ${agentId}`);

    for (const cmd of commands) {
      try {
        const result = await this.terminalManager.executeCommand({
          command: cmd.command,
          workingDirectory: cmd.workingDirectory || process.cwd(),
          timeout: cmd.timeout || 30000,
          agentId,
          metadata: {
            category: cmd.category,
            timestamp: Date.now(),
            source: 'agent_execution'
          }
        });

        outputs.push(`$ ${cmd.command}\n${result}`);
        console.log(`AgentExecutionService: Executed command "${cmd.command}" successfully`);
      } catch (error) {
        const errorMsg = `Command "${cmd.command}" failed: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      output: outputs.join('\n\n'),
      terminalOutput: outputs.join('\n\n'),
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        commandsExecuted: outputs.length,
        errors: errors.length,
        agentId
      }
    };
  }

  /**
   * Update Kanban board
   */
  async updateKanban(updates: KanbanUpdateRequest[], agentId: string): Promise<ExecutionResult> {
    const results: Array<{ cardId: string; action: string; result: any }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Processing ${updates.length} Kanban updates for agent ${agentId}`);

    for (const update of updates) {
      try {
        let result: any;

        switch (update.action) {
          case 'create':
            result = await boardIPCBridge.createCard('main', 'column-1', {
              title: update.data.title || 'Agent Task',
              description: update.data.description || '',
              priority: update.data.priority || 'medium',
              ...update.data
            });
            break;

          case 'update':
            if (update.cardId) {
              result = await boardIPCBridge.updateCard('main', update.cardId, update.data);
            }
            break;

          case 'move':
            if (update.cardId && update.data.columnId) {
              result = await boardIPCBridge.moveCard('main', update.cardId, 'current', update.data.columnId, 'swimlane-1');
            }
            break;

          case 'complete':
            if (update.cardId) {
              result = await boardIPCBridge.updateCardProgress('main', update.cardId, 100, agentId);
            }
            break;

          default:
            throw new Error(`Unknown Kanban action: ${update.action}`);
        }

        if (result) {
          results.push({
            cardId: update.cardId || result.id || 'unknown',
            action: update.action,
            result
          });
          console.log(`AgentExecutionService: Kanban ${update.action} successful`);
        }
      } catch (error) {
        const errorMsg = `Kanban ${update.action} failed: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      output: `Processed ${results.length} Kanban updates`,
      kanbanUpdates: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        updatesProcessed: results.length,
        errors: errors.length,
        agentId
      }
    };
  }

  /**
   * Comprehensive execution method that handles multiple types of work
   */
  async executeWork(
    context: AgentContext,
    agentId: string,
    work: {
      files?: FileCreationRequest[];
      commands?: TerminalCommandRequest[];
      kanban?: KanbanUpdateRequest[];
    }
  ): Promise<ExecutionResult> {
    const allResults: ExecutionResult[] = [];
    const allFiles: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const allOutputs: string[] = [];
    const allErrors: string[] = [];

    console.log(`AgentExecutionService: Executing comprehensive work for agent ${agentId}`);

    // Execute file operations
    if (work.files && work.files.length > 0) {
      const fileResult = await this.createFiles(work.files, agentId);
      allResults.push(fileResult);
      if (fileResult.files) allFiles.push(...fileResult.files);
      allOutputs.push(fileResult.output);
      if (fileResult.error) allErrors.push(fileResult.error);
    }

    // Execute terminal commands
    if (work.commands && work.commands.length > 0) {
      const terminalResult = await this.executeTerminalCommands(work.commands, agentId);
      allResults.push(terminalResult);
      if (terminalResult.terminalOutput) allOutputs.push(terminalResult.terminalOutput);
      if (terminalResult.error) allErrors.push(terminalResult.error);
    }

    // Execute Kanban updates
    if (work.kanban && work.kanban.length > 0) {
      const kanbanResult = await this.updateKanban(work.kanban, agentId);
      allResults.push(kanbanResult);
      allOutputs.push(kanbanResult.output);
      if (kanbanResult.error) allErrors.push(kanbanResult.error);
    }

    const overallSuccess = allErrors.length === 0;
    const combinedOutput = allOutputs.join('\n\n');

    console.log(`AgentExecutionService: Work execution complete. Success: ${overallSuccess}, Files: ${allFiles.length}, Errors: ${allErrors.length}`);

    return {
      success: overallSuccess,
      output: combinedOutput,
      files: allFiles.length > 0 ? allFiles : undefined,
      terminalOutput: allResults.find(r => r.terminalOutput)?.terminalOutput,
      kanbanUpdates: allResults.find(r => r.kanbanUpdates)?.kanbanUpdates,
      error: allErrors.length > 0 ? allErrors.join('; ') : undefined,
      metadata: {
        totalOperations: allResults.length,
        filesCreated: allFiles.length,
        errors: allErrors.length,
        agentId,
        context: {
          task: context.task,
          timestamp: Date.now()
        }
      }
    };
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    files: any;
    terminal: any;
    monaco: any;
  } {
    return {
      files: this.fileManager.getStats(),
      terminal: this.terminalManager.getStats(),
      monaco: this.monacoManager.getStats()
    };
  }
}
