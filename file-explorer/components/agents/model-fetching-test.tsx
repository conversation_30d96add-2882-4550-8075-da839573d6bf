// components/agents/model-fetching-test.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ModelRegistryService } from './model-registry-service';
import { getAllProviders, getProviderConfig, LLMProvider } from './llm-provider-registry';

export const ModelFetchingTest: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>('openai');
  const [apiKey, setApiKey] = useState('');
  const [models, setModels] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cacheStats, setCacheStats] = useState<any>({});

  const modelRegistry = ModelRegistryService.getInstance();

  const isElectronAPIAvailable = (): boolean => {
    return typeof window !== 'undefined' &&
           window.electronAPI?.llm?.fetchModels !== undefined;
  };

  const testModelFetching = async () => {
    if (!apiKey.trim()) {
      setError('Please enter an API key');
      return;
    }

    if (!isElectronAPIAvailable()) {
      setError('Electron API is not available. This feature requires running in Electron mode.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setModels([]);

    try {
      console.log(`Testing model fetching for ${selectedProvider}...`);
      const fetchedModels = await modelRegistry.fetchModels(selectedProvider, apiKey);
      setModels(fetchedModels);
      console.log(`Successfully fetched ${fetchedModels.length} models`);

      // Update cache stats
      setCacheStats(modelRegistry.getCacheStats());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      console.error('Model fetching failed:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const testCachedModels = () => {
    const cached = modelRegistry.getCachedModels(selectedProvider);
    if (cached) {
      setModels(cached);
      setError(null);
      console.log(`Using cached models: ${cached.length} models`);
    } else {
      setError('No cached models available');
      setModels([]);
    }
  };

  const clearCache = () => {
    modelRegistry.clearCache(selectedProvider);
    setCacheStats(modelRegistry.getCacheStats());
    setModels([]);
    console.log(`Cache cleared for ${selectedProvider}`);
  };

  const clearAllCache = () => {
    modelRegistry.clearCache();
    setCacheStats(modelRegistry.getCacheStats());
    setModels([]);
    console.log('All cache cleared');
  };

  const refreshCacheStats = () => {
    setCacheStats(modelRegistry.getCacheStats());
  };

  const config = getProviderConfig(selectedProvider);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Dynamic Model Fetching Test</CardTitle>
        <CardDescription>
          Test the real-time model fetching functionality for LLM providers
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Provider Selection */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Provider</Label>
            <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {getAllProviders().map(provider => (
                  <SelectItem key={provider} value={provider}>
                    {getProviderConfig(provider).name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>API Key</Label>
            <Input
              type="password"
              placeholder="Enter API key for testing"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
            />
          </div>
        </div>

        {/* Provider Info */}
        <div className="flex items-center gap-2">
          <Badge variant={config.supportsModelFetching ? "default" : "secondary"}>
            {config.supportsModelFetching ? "Supports Dynamic Fetching" : "Static Models Only"}
          </Badge>
          <Badge variant={isElectronAPIAvailable() ? "success" : "destructive"}>
            {isElectronAPIAvailable() ? "Electron API Available" : "Electron API Required"}
          </Badge>
          {config.modelsEndpoint && (
            <Badge variant="outline">
              Endpoint: {config.modelsEndpoint}
            </Badge>
          )}
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={testModelFetching}
            disabled={isLoading || !config.supportsModelFetching || !isElectronAPIAvailable()}
          >
            {isLoading ? 'Fetching...' : 'Fetch Models'}
          </Button>
          <Button
            onClick={testCachedModels}
            variant="outline"
            disabled={isLoading}
          >
            Use Cached
          </Button>
          <Button
            onClick={clearCache}
            variant="secondary"
            disabled={isLoading}
          >
            Clear Cache
          </Button>
          <Button
            onClick={clearAllCache}
            variant="secondary"
            disabled={isLoading}
          >
            Clear All Cache
          </Button>
          <Button
            onClick={refreshCacheStats}
            variant="ghost"
            disabled={isLoading}
          >
            Refresh Stats
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-300 text-sm">
              <strong>Error:</strong> {error}
            </p>
          </div>
        )}

        {/* Models Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Fetched Models ({models.length})</Label>
            {models.length > 0 && (
              <Badge variant="success">
                {models.length} models loaded
              </Badge>
            )}
          </div>
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg max-h-64 overflow-y-auto">
            {models.length === 0 ? (
              <p className="text-gray-500">No models fetched yet. Click "Fetch Models" to start.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {models.map((model, index) => (
                  <div key={index} className="p-2 bg-white dark:bg-gray-800 rounded border text-sm">
                    {model}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Cache Statistics */}
        <div className="space-y-2">
          <Label>Cache Statistics</Label>
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
            {Object.keys(cacheStats).length === 0 ? (
              <p className="text-gray-500">No cache data available</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(cacheStats).map(([provider, stats]: [string, any]) => (
                  <div key={provider} className="p-3 bg-white dark:bg-gray-800 rounded border">
                    <h4 className="font-medium text-sm mb-2">{provider}</h4>
                    <div className="space-y-1 text-xs">
                      <div>Models: {stats.count}</div>
                      <div>Age: {stats.age}s</div>
                      <div className={`font-medium ${stats.valid ? 'text-green-600' : 'text-red-600'}`}>
                        {stats.valid ? 'Valid' : 'Expired'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Test Instructions:</h4>
          <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
            <li>1. <strong>Electron Required:</strong> This feature only works in Electron mode, not in browser</li>
            <li>2. Select a provider that supports dynamic fetching (OpenAI, OpenRouter, etc.)</li>
            <li>3. Enter a valid API key for the selected provider</li>
            <li>4. Click "Fetch Models" to test real-time model fetching</li>
            <li>5. Use "Use Cached" to test cache functionality</li>
            <li>6. Check cache statistics to verify caching behavior</li>
          </ul>
          {!isElectronAPIAvailable() && (
            <div className="mt-3 p-2 bg-orange-100 dark:bg-orange-900/30 border border-orange-300 dark:border-orange-700 rounded">
              <p className="text-orange-800 dark:text-orange-200 text-sm">
                <strong>Note:</strong> Dynamic model fetching is disabled because Electron API is not available.
                Run the application in Electron mode to test this feature.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ModelFetchingTest;
