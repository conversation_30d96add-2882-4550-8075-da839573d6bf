// components/agents/model-metadata.ts
// Centralized metadata for all known models across providers
// Based on official documentation and public pricing tables

export interface ModelMetadata {
  provider: string;
  modelId: string;
  label: string;
  contextSize: number; // in tokens
  pricing: {
    input: number; // USD per 1K input tokens
    output: number; // USD per 1K output tokens
  };
  tags: string[];
  notes?: string;
  releaseDate?: string;
  deprecated?: boolean;
}

export const MODEL_METADATA: ModelMetadata[] = [
  // ===== ANTHROPIC MODELS =====
  // Claude 4 Series (Latest Generation)
  {
    provider: 'anthropic',
    modelId: 'claude-opus-4-20250514',
    label: 'Claude Opus 4',
    contextSize: 200_000,
    pricing: { input: 0.015, output: 0.075 },
    tags: ['advanced reasoning', 'complex analysis', 'creative writing', 'code generation'],
    notes: 'Most powerful Claude model with exceptional reasoning capabilities',
    releaseDate: '2025-05-14'
  },
  {
    provider: 'anthropic',
    modelId: 'claude-sonnet-4-20250514',
    label: 'Claude Sonnet 4',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['general purpose', 'balanced performance', 'code generation', 'analysis'],
    notes: 'Balanced performance and speed for most use cases',
    releaseDate: '2025-05-14'
  },

  // Claude 3.7 Series (Enhanced 3.5)
  {
    provider: 'anthropic',
    modelId: 'claude-3-7-sonnet-20250219',
    label: 'Claude Sonnet 3.7 (Latest)',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['enhanced reasoning', 'code generation', 'analysis', 'writing'],
    notes: 'Enhanced version of Claude 3.5 with improved capabilities',
    releaseDate: '2025-02-19'
  },

  // Claude 3.5 Series (Current Production)
  {
    provider: 'anthropic',
    modelId: 'claude-3-5-haiku-20241022',
    label: 'Claude Haiku 3.5 (Latest)',
    contextSize: 200_000,
    pricing: { input: 0.00025, output: 0.00125 },
    tags: ['fast responses', 'simple tasks', 'quick analysis', 'cost-effective'],
    notes: 'Fastest Claude model optimized for speed and efficiency',
    releaseDate: '2024-10-22'
  },
  {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-20241022',
    label: 'Claude Sonnet 3.5 v2 (Latest)',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['general purpose', 'code generation', 'analysis', 'writing'],
    notes: 'Latest version of Claude 3.5 Sonnet with improved performance',
    releaseDate: '2024-10-22'
  },
  {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-20240620',
    label: 'Claude Sonnet 3.5',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['general purpose', 'code generation', 'analysis', 'writing'],
    notes: 'Original Claude 3.5 Sonnet release',
    releaseDate: '2024-06-20'
  },

  // Claude 3 Series (Stable Production)
  {
    provider: 'anthropic',
    modelId: 'claude-3-opus-20240229',
    label: 'Claude Opus 3',
    contextSize: 200_000,
    pricing: { input: 0.015, output: 0.075 },
    tags: ['advanced reasoning', 'complex analysis', 'creative writing'],
    notes: 'Most powerful Claude 3 model for complex reasoning tasks',
    releaseDate: '2024-02-29'
  },
  {
    provider: 'anthropic',
    modelId: 'claude-3-sonnet-20240229',
    label: 'Claude Sonnet 3',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['general purpose', 'analysis', 'writing'],
    notes: 'Balanced Claude 3 model for general use',
    releaseDate: '2024-02-29'
  },
  {
    provider: 'anthropic',
    modelId: 'claude-3-haiku-20240307',
    label: 'Claude Haiku 3',
    contextSize: 200_000,
    pricing: { input: 0.00025, output: 0.00125 },
    tags: ['fast responses', 'simple tasks', 'cost-effective'],
    notes: 'Fastest Claude 3 model for quick responses',
    releaseDate: '2024-03-07'
  },

  // ===== OPENAI MODELS =====
  // GPT-4 Series
  {
    provider: 'openai',
    modelId: 'gpt-4o',
    label: 'GPT-4o',
    contextSize: 128_000,
    pricing: { input: 0.0025, output: 0.01 },
    tags: ['multimodal', 'vision', 'audio', 'general purpose'],
    notes: 'Most advanced multimodal model with vision and audio capabilities',
    releaseDate: '2024-05-13'
  },
  {
    provider: 'openai',
    modelId: 'gpt-4o-mini',
    label: 'GPT-4o Mini',
    contextSize: 128_000,
    pricing: { input: 0.00015, output: 0.0006 },
    tags: ['cost-effective', 'fast', 'multimodal', 'vision'],
    notes: 'Affordable and intelligent small model for fast, lightweight tasks',
    releaseDate: '2024-07-18'
  },
  {
    provider: 'openai',
    modelId: 'gpt-4-turbo',
    label: 'GPT-4 Turbo',
    contextSize: 128_000,
    pricing: { input: 0.01, output: 0.03 },
    tags: ['long context', 'general purpose', 'coding', 'analysis'],
    notes: 'Latest GPT-4 Turbo with improved instruction following',
    releaseDate: '2024-04-09'
  },
  {
    provider: 'openai',
    modelId: 'gpt-4',
    label: 'GPT-4',
    contextSize: 8_192,
    pricing: { input: 0.03, output: 0.06 },
    tags: ['general purpose', 'reasoning', 'creative writing'],
    notes: 'Original GPT-4 model with strong reasoning capabilities',
    releaseDate: '2023-03-14'
  },

  // GPT-3.5 Series
  {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    contextSize: 16_385,
    pricing: { input: 0.0005, output: 0.0015 },
    tags: ['cost-effective', 'fast', 'general purpose', 'chat'],
    notes: 'Fast, inexpensive model for simple tasks',
    releaseDate: '2023-03-01'
  },

  // ===== OPENROUTER MODELS =====
  // Popular OpenRouter Models
  {
    provider: 'openrouter',
    modelId: 'meta-llama/llama-3.1-405b-instruct',
    label: 'Llama 3.1 405B Instruct',
    contextSize: 131_072,
    pricing: { input: 0.005, output: 0.005 },
    tags: ['large model', 'instruction following', 'reasoning', 'multilingual'],
    notes: 'Largest Llama model with exceptional capabilities',
    releaseDate: '2024-07-23'
  },
  {
    provider: 'openrouter',
    modelId: 'meta-llama/llama-3.1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    contextSize: 131_072,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['instruction following', 'reasoning', 'cost-effective', 'multilingual'],
    notes: 'High-performance Llama model with great cost-performance ratio',
    releaseDate: '2024-07-23'
  },
  {
    provider: 'openrouter',
    modelId: 'mistralai/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    contextSize: 32_768,
    pricing: { input: 0.00024, output: 0.00024 },
    tags: ['mixture of experts', 'fast', 'cost-effective', 'multilingual'],
    notes: 'Efficient mixture-of-experts model with strong performance',
    releaseDate: '2023-12-11'
  },
  {
    provider: 'openrouter',
    modelId: 'anthropic/claude-3-sonnet',
    label: 'Claude 3 Sonnet (OpenRouter)',
    contextSize: 200_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['general purpose', 'long context', 'analysis', 'writing'],
    notes: 'Claude 3 Sonnet via OpenRouter with competitive pricing',
    releaseDate: '2024-02-29'
  },
  {
    provider: 'openrouter',
    modelId: 'openai/gpt-4o',
    label: 'GPT-4o (OpenRouter)',
    contextSize: 128_000,
    pricing: { input: 0.0025, output: 0.01 },
    tags: ['multimodal', 'vision', 'general purpose', 'reasoning'],
    notes: 'GPT-4o via OpenRouter with multimodal capabilities',
    releaseDate: '2024-05-13'
  },
  {
    provider: 'openrouter',
    modelId: 'google/gemini-pro',
    label: 'Gemini Pro (OpenRouter)',
    contextSize: 32_768,
    pricing: { input: 0.0005, output: 0.0015 },
    tags: ['multimodal', 'general purpose', 'cost-effective'],
    notes: 'Google Gemini Pro via OpenRouter',
    releaseDate: '2023-12-06'
  },
  {
    provider: 'openrouter',
    modelId: 'cohere/command-r-plus',
    label: 'Command R+ (OpenRouter)',
    contextSize: 128_000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['reasoning', 'analysis', 'long context', 'enterprise'],
    notes: 'Cohere Command R+ optimized for complex reasoning tasks',
    releaseDate: '2024-04-04'
  },

  // ===== GOOGLE AI MODELS =====
  {
    provider: 'google',
    modelId: 'gemini-1.5-pro',
    label: 'Gemini 1.5 Pro',
    contextSize: 2_000_000,
    pricing: { input: 0.00125, output: 0.00375 },
    tags: ['ultra-long context', 'multimodal', 'reasoning', 'code generation'],
    notes: 'Advanced model with exceptional long context capabilities',
    releaseDate: '2024-02-15'
  },
  {
    provider: 'google',
    modelId: 'gemini-1.5-flash',
    label: 'Gemini 1.5 Flash',
    contextSize: 1_000_000,
    pricing: { input: 0.000075, output: 0.0003 },
    tags: ['fast', 'cost-effective', 'long context', 'multimodal'],
    notes: 'Fast and cost-effective model with long context support',
    releaseDate: '2024-05-14'
  },
  {
    provider: 'google',
    modelId: 'gemini-pro',
    label: 'Gemini Pro',
    contextSize: 32_768,
    pricing: { input: 0.0005, output: 0.0015 },
    tags: ['general purpose', 'multimodal', 'reasoning'],
    notes: 'Balanced model for various tasks with multimodal capabilities',
    releaseDate: '2023-12-06'
  },

  // ===== DEEPSEEK MODELS =====
  {
    provider: 'deepseek',
    modelId: 'deepseek-chat',
    label: 'DeepSeek Chat',
    contextSize: 32_768,
    pricing: { input: 0.00014, output: 0.00028 },
    tags: ['cost-effective', 'general purpose', 'chat', 'reasoning'],
    notes: 'Affordable general-purpose model with strong performance',
    releaseDate: '2024-01-15'
  },
  {
    provider: 'deepseek',
    modelId: 'deepseek-coder',
    label: 'DeepSeek Coder',
    contextSize: 32_768,
    pricing: { input: 0.00014, output: 0.00028 },
    tags: ['code generation', 'programming', 'debugging', 'cost-effective'],
    notes: 'Specialized model for coding and programming tasks',
    releaseDate: '2024-01-15'
  },

  // ===== FIREWORKS AI MODELS =====
  {
    provider: 'fireworks',
    modelId: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    contextSize: 131_072,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['instruction following', 'reasoning', 'fast inference', 'cost-effective'],
    notes: 'High-performance Llama model optimized for fast inference',
    releaseDate: '2024-07-23'
  },
  {
    provider: 'fireworks',
    modelId: 'accounts/fireworks/models/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    contextSize: 32_768,
    pricing: { input: 0.0005, output: 0.0005 },
    tags: ['mixture of experts', 'fast', 'cost-effective', 'multilingual'],
    notes: 'Efficient mixture-of-experts model with optimized inference',
    releaseDate: '2023-12-11'
  }
];

// Helper functions for working with model metadata
export function getModelMetadata(provider: string, modelId: string): ModelMetadata | undefined {
  return MODEL_METADATA.find(model =>
    model.provider === provider && model.modelId === modelId
  );
}

export function getModelsByProvider(provider: string): ModelMetadata[] {
  return MODEL_METADATA.filter(model => model.provider === provider);
}

export function getModelsByTag(tag: string): ModelMetadata[] {
  return MODEL_METADATA.filter(model =>
    model.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
  );
}

export function formatContextSize(tokens: number): string {
  if (tokens >= 1_000_000) {
    return `${(tokens / 1_000_000).toFixed(1)}M tokens`;
  } else if (tokens >= 1_000) {
    return `${(tokens / 1_000).toFixed(0)}K tokens`;
  } else {
    return `${tokens} tokens`;
  }
}

export function formatPricing(pricing: { input: number; output: number }): string {
  return `$${pricing.input.toFixed(4)}/$${pricing.output.toFixed(4)} per 1K tokens`;
}

export function isModelDeprecated(provider: string, modelId: string): boolean {
  const metadata = getModelMetadata(provider, modelId);
  return metadata?.deprecated || false;
}

export function getModelTags(provider: string, modelId: string): string[] {
  const metadata = getModelMetadata(provider, modelId);
  return metadata?.tags || [];
}

export function getModelNotes(provider: string, modelId: string): string | undefined {
  const metadata = getModelMetadata(provider, modelId);
  return metadata?.notes;
}

export default MODEL_METADATA;
