// components/agents/anthropic-models-test.tsx
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AnthropicModelSelector } from './anthropic-model-selector';
import { 
  ANTHROPIC_MODELS, 
  getAnthropicModelById, 
  getAnthropicModelOptions,
  getLatestAnthropicModels,
  getAnthropicModelsByCapability,
  isValidAnthropicModel,
  getAnthropicModelDescription,
  getAnthropicModelCost
} from './anthropic-models';

export const AnthropicModelsTest: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState('claude-3-5-sonnet-20241022');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testModelFunctions = () => {
    addResult('🧪 Testing Anthropic model functions...');
    
    // Test model count
    addResult(`✅ Total models available: ${ANTHROPIC_MODELS.length}`);
    
    // Test latest models
    const latestModels = getLatestAnthropicModels();
    addResult(`✅ Latest models: ${latestModels.length} (${latestModels.map(m => m.label).join(', ')})`);
    
    // Test model by capability
    const codeModels = getAnthropicModelsByCapability('code_generation');
    addResult(`✅ Code generation models: ${codeModels.length}`);
    
    // Test model validation
    const validModel = isValidAnthropicModel('claude-3-5-sonnet-20241022');
    const invalidModel = isValidAnthropicModel('invalid-model-id');
    addResult(`✅ Model validation: valid=${validModel}, invalid=${invalidModel}`);
    
    // Test model description
    const description = getAnthropicModelDescription('claude-opus-4-20250514');
    addResult(`✅ Model description: ${description.substring(0, 50)}...`);
    
    // Test model cost
    const cost = getAnthropicModelCost('claude-3-haiku-20240307');
    addResult(`✅ Model cost: ${cost ? `$${cost.input}/$${cost.output}` : 'N/A'}`);
  };

  const testModelSeries = () => {
    addResult('🧪 Testing model series...');
    
    const claude4Models = ANTHROPIC_MODELS.filter(m => m.id.includes('claude-opus-4') || m.id.includes('claude-sonnet-4'));
    addResult(`✅ Claude 4 models: ${claude4Models.length} (${claude4Models.map(m => m.label).join(', ')})`);
    
    const claude37Models = ANTHROPIC_MODELS.filter(m => m.id.includes('claude-3-7'));
    addResult(`✅ Claude 3.7 models: ${claude37Models.length} (${claude37Models.map(m => m.label).join(', ')})`);
    
    const claude35Models = ANTHROPIC_MODELS.filter(m => m.id.includes('claude-3-5'));
    addResult(`✅ Claude 3.5 models: ${claude35Models.length} (${claude35Models.map(m => m.label).join(', ')})`);
    
    const claude3Models = ANTHROPIC_MODELS.filter(m => m.id.includes('claude-3-') && !m.id.includes('claude-3-5') && !m.id.includes('claude-3-7'));
    addResult(`✅ Claude 3 models: ${claude3Models.length} (${claude3Models.map(m => m.label).join(', ')})`);
  };

  const testModelDetails = () => {
    addResult('🧪 Testing model details...');
    
    ANTHROPIC_MODELS.forEach(model => {
      const hasDescription = !!model.description;
      const hasContextLength = !!model.contextLength;
      const hasCost = !!model.costPer1kTokens;
      const hasCapabilities = !!model.capabilities && model.capabilities.length > 0;
      
      addResult(`✅ ${model.label}: desc=${hasDescription}, context=${hasContextLength}, cost=${hasCost}, caps=${hasCapabilities}`);
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const selectedModelData = getAnthropicModelById(selectedModel);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Anthropic Models Test Suite</CardTitle>
        <CardDescription>
          Test the updated Anthropic models list and selector component
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Model Selector Test */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Model Selector Test</h3>
          <AnthropicModelSelector
            value={selectedModel}
            onChange={setSelectedModel}
            placeholder="Select an Anthropic model"
          />
        </div>

        {/* Model Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-3 bg-muted rounded-lg text-center">
            <div className="text-2xl font-bold">{ANTHROPIC_MODELS.length}</div>
            <div className="text-sm text-muted-foreground">Total Models</div>
          </div>
          <div className="p-3 bg-muted rounded-lg text-center">
            <div className="text-2xl font-bold">{ANTHROPIC_MODELS.filter(m => m.id.includes('claude-opus-4') || m.id.includes('claude-sonnet-4')).length}</div>
            <div className="text-sm text-muted-foreground">Claude 4 Models</div>
          </div>
          <div className="p-3 bg-muted rounded-lg text-center">
            <div className="text-2xl font-bold">{ANTHROPIC_MODELS.filter(m => m.id.includes('claude-3-5')).length}</div>
            <div className="text-sm text-muted-foreground">Claude 3.5 Models</div>
          </div>
          <div className="p-3 bg-muted rounded-lg text-center">
            <div className="text-2xl font-bold">{getLatestAnthropicModels().length}</div>
            <div className="text-sm text-muted-foreground">Latest Models</div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={testModelFunctions} variant="outline">
            Test Model Functions
          </Button>
          <Button onClick={testModelSeries} variant="outline">
            Test Model Series
          </Button>
          <Button onClick={testModelDetails} variant="outline">
            Test Model Details
          </Button>
          <Button onClick={clearResults} variant="secondary">
            Clear Results
          </Button>
        </div>

        {/* Selected Model Info */}
        {selectedModelData && (
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-semibold mb-2">Selected Model: {selectedModelData.label}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>ID:</strong> {selectedModelData.id}
              </div>
              <div>
                <strong>Release Date:</strong> {selectedModelData.releaseDate || 'N/A'}
              </div>
              {selectedModelData.contextLength && (
                <div>
                  <strong>Context Length:</strong> {selectedModelData.contextLength.toLocaleString()} tokens
                </div>
              )}
              {selectedModelData.costPer1kTokens && (
                <div>
                  <strong>Cost per 1K tokens:</strong> ${selectedModelData.costPer1kTokens.input}/${selectedModelData.costPer1kTokens.output}
                </div>
              )}
            </div>
            {selectedModelData.description && (
              <p className="mt-2 text-sm text-muted-foreground">
                {selectedModelData.description}
              </p>
            )}
            {selectedModelData.capabilities && selectedModelData.capabilities.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {selectedModelData.capabilities.map((capability) => (
                  <Badge key={capability} variant="outline" className="text-xs">
                    {capability.replace(/_/g, ' ')}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Test Results */}
        <div className="space-y-2">
          <h4 className="font-semibold">Test Results</h4>
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index} className="whitespace-pre-wrap">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Model List Preview */}
        <div className="space-y-2">
          <h4 className="font-semibold">All Available Models</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
            {ANTHROPIC_MODELS.map((model) => (
              <div key={model.id} className="p-2 bg-muted rounded text-sm">
                <div className="font-medium">{model.label}</div>
                <div className="text-xs text-muted-foreground">{model.id}</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AnthropicModelsTest;
