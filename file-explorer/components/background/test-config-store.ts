// components/background/test-config-store.ts
// Test script to verify SQLite Configuration Store functionality

import { getDatabaseManager, getConfigStore, createDefaultProjectConfig } from './index';

export async function testConfigStore(): Promise<void> {
  console.log('🧪 Testing SQLite Configuration Store...');

  try {
    // Test 1: Initialize systems
    console.log('1. Initializing database and config store...');
    const dbManager = getDatabaseManager();
    await dbManager.initialize();
    
    const configStore = getConfigStore();
    await configStore.initialize();
    console.log('✅ Initialization successful');

    // Test 2: Global Settings
    console.log('2. Testing global settings...');
    await configStore.setGlobalSetting('test', 'string_value', 'Hello World');
    await configStore.setGlobalSetting('test', 'number_value', 42);
    await configStore.setGlobalSetting('test', 'boolean_value', true);
    await configStore.setGlobalSetting('test', 'object_value', { key: 'value', nested: { data: 123 } });
    await configStore.setGlobalSetting('test', 'array_value', [1, 2, 3, 'test']);

    const stringValue = await configStore.getGlobalSetting('test', 'string_value');
    const numberValue = await configStore.getGlobalSetting('test', 'number_value');
    const booleanValue = await configStore.getGlobalSetting('test', 'boolean_value');
    const objectValue = await configStore.getGlobalSetting('test', 'object_value');
    const arrayValue = await configStore.getGlobalSetting('test', 'array_value');

    console.log('Global settings test results:');
    console.log('  String:', stringValue === 'Hello World' ? '✅' : '❌', stringValue);
    console.log('  Number:', numberValue === 42 ? '✅' : '❌', numberValue);
    console.log('  Boolean:', booleanValue === true ? '✅' : '❌', booleanValue);
    console.log('  Object:', JSON.stringify(objectValue) === JSON.stringify({ key: 'value', nested: { data: 123 } }) ? '✅' : '❌', objectValue);
    console.log('  Array:', JSON.stringify(arrayValue) === JSON.stringify([1, 2, 3, 'test']) ? '✅' : '❌', arrayValue);

    // Test 3: Project Configuration
    console.log('3. Testing project configuration...');
    const projectConfig = createDefaultProjectConfig('/test/project/path', 'react-typescript');
    projectConfig.name = 'Test Project';
    
    const createdProject = await configStore.createProject(projectConfig);
    console.log('✅ Project created:', createdProject.id);

    const retrievedProject = await configStore.getProject(createdProject.id);
    console.log('✅ Project retrieved:', retrievedProject?.name === 'Test Project' ? '✅' : '❌');

    const projectByPath = await configStore.getProjectByPath('/test/project/path');
    console.log('✅ Project found by path:', projectByPath?.id === createdProject.id ? '✅' : '❌');

    // Test 4: Project Updates
    console.log('4. Testing project updates...');
    const updatedProject = await configStore.updateProject(createdProject.id, {
      name: 'Updated Test Project',
      settings: { ...createdProject.settings, newSetting: 'test' }
    });
    console.log('✅ Project updated:', updatedProject?.name === 'Updated Test Project' ? '✅' : '❌');

    // Test 5: List all projects
    console.log('5. Testing project listing...');
    const allProjects = await configStore.getAllProjects();
    console.log('✅ Projects listed:', allProjects.length >= 1 ? '✅' : '❌', `(${allProjects.length} projects)`);

    // Test 6: Global settings by category
    console.log('6. Testing settings by category...');
    const testSettings = await configStore.getGlobalSettingsByCategory('test');
    console.log('✅ Settings by category:', Object.keys(testSettings).length === 5 ? '✅' : '❌', Object.keys(testSettings));

    // Test 7: Database operations
    console.log('7. Testing database operations...');
    const dbPath = dbManager.getDbPath();
    console.log('✅ Database path:', dbPath);
    
    // Test query
    const projects = dbManager.query('SELECT COUNT(*) as count FROM projects');
    console.log('✅ Direct query:', projects[0]?.count >= 1 ? '✅' : '❌', `(${projects[0]?.count} projects in DB)`);

    // Test 8: Cleanup test data
    console.log('8. Cleaning up test data...');
    await configStore.deleteProject(createdProject.id);
    await configStore.deleteGlobalSetting('test', 'string_value');
    await configStore.deleteGlobalSetting('test', 'number_value');
    await configStore.deleteGlobalSetting('test', 'boolean_value');
    await configStore.deleteGlobalSetting('test', 'object_value');
    await configStore.deleteGlobalSetting('test', 'array_value');
    console.log('✅ Test data cleaned up');

    console.log('🎉 All tests passed! SQLite Configuration Store is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

export async function testSettingsManagerIntegration(): Promise<void> {
  console.log('🧪 Testing Settings Manager Integration...');

  try {
    const { SettingsManager } = await import('../settings/settings-manager');
    const settingsManager = new SettingsManager();

    // Wait a bit for async initialization
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('1. Testing settings manager initialization...');
    console.log('✅ Settings manager created');

    // Test settings persistence
    console.log('2. Testing settings persistence...');
    settingsManager.updateSystemSettings({ debugMode: true });
    settingsManager.setApiKey('openai', 'test-key-123');
    
    // Create a new instance to test persistence
    const settingsManager2 = new SettingsManager();
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const settings = settingsManager2.getSettings();
    console.log('✅ Settings persistence:', settings.system.debugMode === true ? '✅' : '❌');
    console.log('✅ API key persistence:', settings.apiKeys.openai === 'test-key-123' ? '✅' : '❌');

    // Test project management
    console.log('3. Testing project management...');
    const projectId = await settingsManager.createProject('Test Integration Project', '/test/integration/path');
    console.log('✅ Project created via settings manager:', projectId);

    const project = await settingsManager.getProject(projectId);
    console.log('✅ Project retrieved:', project?.name === 'Test Integration Project' ? '✅' : '❌');

    const allProjects = await settingsManager.getAllProjects();
    console.log('✅ Projects listed:', allProjects.length >= 1 ? '✅' : '❌');

    // Cleanup
    await settingsManager.deleteProject(projectId);
    settingsManager.removeApiKey('openai');
    settingsManager.updateSystemSettings({ debugMode: false });

    console.log('🎉 Settings Manager integration tests passed!');

  } catch (error) {
    console.error('❌ Settings Manager integration test failed:', error);
    throw error;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  (async () => {
    try {
      await testConfigStore();
      await testSettingsManagerIntegration();
      console.log('🎉 All tests completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Tests failed:', error);
      process.exit(1);
    }
  })();
}
