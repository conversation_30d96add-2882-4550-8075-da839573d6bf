// components/agents/model-registry-service.ts
import { LLMProvider, getProviderConfig } from './llm-provider-registry';
import { getModelMetadata, getModelsByProvider, ModelMetadata } from './model-metadata';

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
  owned_by?: string;
  created?: number;
  metadata?: ModelMetadata;
}

export interface ModelCacheEntry {
  models: ModelInfo[];
  timestamp: number;
  ttl: number;
}

export class ModelRegistryService {
  private static instance: ModelRegistryService;
  private modelCache: Map<LLMProvider, ModelCacheEntry> = new Map();
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_RETRIES = 3;

  private constructor() {}

  public static getInstance(): ModelRegistryService {
    if (!ModelRegistryService.instance) {
      ModelRegistryService.instance = new ModelRegistryService();
    }
    return ModelRegistryService.instance;
  }

  /**
   * Fetch models for a specific provider with caching
   */
  public async fetchModels(provider: LLMProvider, apiKey: string, forceRefresh = false): Promise<string[]> {
    try {
      // Check cache first
      if (!forceRefresh && this.isCacheValid(provider)) {
        const cached = this.modelCache.get(provider);
        if (cached) {
          console.log(`ModelRegistryService: Using cached models for ${provider}`);
          return cached.models.map(m => m.id);
        }
      }

      const config = getProviderConfig(provider);

      if (!config.supportsModelFetching) {
        console.log(`ModelRegistryService: ${provider} doesn't support model fetching, using static list`);
        return this.getStaticModels(provider);
      }

      // Check if Electron API is available
      if (!this.isElectronAPIAvailable()) {
        console.log(`ModelRegistryService: Electron API not available, falling back to static models for ${provider}`);
        return this.getStaticModels(provider);
      }

      console.log(`ModelRegistryService: Fetching models for ${provider} via Electron API...`);

      // Use Electron IPC to fetch models
      const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);
      const models: ModelInfo[] = modelIds.map(id => {
        const metadata = getModelMetadata(provider, id);
        return {
          id,
          name: metadata?.label || id,
          description: metadata?.notes || `${config.name} model: ${id}`,
          contextLength: metadata?.contextSize,
          pricing: metadata?.pricing,
          metadata
        };
      });

      // Cache the results
      this.cacheModels(provider, models);

      return models.map(m => m.id);

    } catch (error) {
      console.error(`ModelRegistryService: Failed to fetch models for ${provider}:`, error);

      // Return cached models if available, otherwise static models
      const cached = this.modelCache.get(provider);
      if (cached) {
        console.log(`ModelRegistryService: Using stale cache for ${provider} due to error`);
        return cached.models.map(m => m.id);
      }

      console.log(`ModelRegistryService: Falling back to static models for ${provider}`);
      return this.getStaticModels(provider);
    }
  }

  /**
   * Check if Electron API is available for model fetching
   */
  private isElectronAPIAvailable(): boolean {
    return typeof window !== 'undefined' &&
           window.electronAPI?.llm?.fetchModels !== undefined;
  }

  /**
   * Get static models for providers that don't support dynamic fetching
   */
  private getStaticModels(provider: LLMProvider): string[] {
    const config = getProviderConfig(provider);
    const staticModelIds = Object.keys(config.modelMap);

    // For providers without dynamic fetching, also include models from metadata
    const metadataModels = getModelsByProvider(provider);
    const metadataModelIds = metadataModels.map(m => m.modelId);

    // Combine and deduplicate
    const allModelIds = [...new Set([...staticModelIds, ...metadataModelIds])];

    console.log(`ModelRegistryService: Using ${allModelIds.length} static models for ${provider}`);
    return allModelIds;
  }

  /**
   * Cache models for a provider
   */
  private cacheModels(provider: LLMProvider, models: ModelInfo[]): void {
    this.modelCache.set(provider, {
      models,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    });
    console.log(`ModelRegistryService: Cached ${models.length} models for ${provider}`);
  }

  /**
   * Check if cache is valid for a provider
   */
  private isCacheValid(provider: LLMProvider): boolean {
    const cached = this.modelCache.get(provider);
    if (!cached) return false;

    const age = Date.now() - cached.timestamp;
    return age < cached.ttl;
  }

  /**
   * Clear cache for a specific provider or all providers
   */
  public clearCache(provider?: LLMProvider): void {
    if (provider) {
      this.modelCache.delete(provider);
      console.log(`ModelRegistryService: Cleared cache for ${provider}`);
    } else {
      this.modelCache.clear();
      console.log('ModelRegistryService: Cleared all model cache');
    }
  }

  /**
   * Get cached models without fetching
   */
  public getCachedModels(provider: LLMProvider): string[] | null {
    const cached = this.modelCache.get(provider);
    if (cached && this.isCacheValid(provider)) {
      return cached.models.map(m => m.id);
    }
    return null;
  }

  /**
   * Get enriched model information with metadata
   */
  public getEnrichedModels(provider: LLMProvider): ModelInfo[] {
    const modelIds = this.getStaticModels(provider);
    return modelIds.map(id => {
      const metadata = getModelMetadata(provider, id);
      const config = getProviderConfig(provider);
      return {
        id,
        name: metadata?.label || id,
        description: metadata?.notes || `${config.name} model: ${id}`,
        contextLength: metadata?.contextSize,
        pricing: metadata?.pricing,
        metadata
      };
    });
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): Record<LLMProvider, { count: number; age: number; valid: boolean }> {
    const stats: any = {};

    for (const [provider, cache] of this.modelCache.entries()) {
      const age = Date.now() - cache.timestamp;
      stats[provider] = {
        count: cache.models.length,
        age: Math.round(age / 1000), // in seconds
        valid: age < cache.ttl
      };
    }

    return stats;
  }
}
