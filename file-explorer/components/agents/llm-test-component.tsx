// components/agents/llm-test-component.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LLMRequestService } from './llm-request-service';
import { getAllProviders } from './llm-provider-registry';

export const LLMTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testApiKey, setTestApiKey] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<string>('openai');

  const llmService = LLMRequestService.getInstance();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testElectronAPI = async () => {
    setIsLoading(true);
    addResult('🧪 Testing Electron API availability...');

    try {
      if (typeof window !== 'undefined' && window.electronAPI?.llm) {
        addResult('✅ Electron LLM API is available');

        // Test API key validation
        if (testApiKey) {
          addResult(`🔑 Testing API key validation for ${selectedProvider}...`);
          const isValid = await window.electronAPI.llm.validateApiKey(selectedProvider, testApiKey);
          addResult(`${isValid ? '✅' : '❌'} API key validation result: ${isValid}`);
        } else {
          addResult('⚠️ No API key provided for validation test');
        }
      } else {
        addResult('❌ Electron LLM API is not available');
        addResult('ℹ️ This might be because you\'re running in browser mode');
      }
    } catch (error) {
      addResult(`❌ Error testing Electron API: ${error instanceof Error ? error.message : String(error)}`);
    }

    setIsLoading(false);
  };

  const testLLMService = async () => {
    setIsLoading(true);
    addResult('🧪 Testing LLM Request Service...');

    try {
      // Test provider registry
      const providers = getAllProviders();
      addResult(`✅ Found ${providers.length} providers: ${providers.join(', ')}`);

      // Test API key validation
      if (testApiKey && selectedProvider) {
        addResult(`🔑 Testing ${selectedProvider} API key validation...`);
        const startTime = Date.now();
        const isValid = await llmService.validateApiKey(selectedProvider as any, testApiKey);
        const duration = Date.now() - startTime;
        addResult(`${isValid ? '✅' : '❌'} API key validation: ${isValid} (${duration}ms)`);

        if (isValid) {
          addResult(`🎉 ${selectedProvider} API key is valid and working!`);
        } else {
          addResult(`⚠️ ${selectedProvider} API key validation failed - check the key and try again`);
        }
      } else {
        addResult('⚠️ No API key or provider selected for validation');
      }

    } catch (error) {
      addResult(`❌ Error testing LLM service: ${error instanceof Error ? error.message : String(error)}`);
    }

    setIsLoading(false);
  };

  const testAgentExecution = async () => {
    setIsLoading(true);
    addResult('🤖 Testing Agent Execution...');

    try {
      // Import and test InternAgent
      const { InternAgent } = await import('./implementation/intern-agent');

      const agentConfig = {
        id: 'test-intern',
        name: 'Test Intern',
        type: 'implementation',
        model: 'gpt-3.5-turbo',
        provider: selectedProvider as any,
        maxTokens: 100,
        temperature: 0.3
      };

      const agent = new InternAgent(agentConfig);
      addResult('✅ InternAgent created successfully');

      const context = {
        task: 'Write a simple hello world function in TypeScript',
        files: [],
        codeContext: '',
        rules: ['Keep it simple', 'Use TypeScript'],
        dependencies: []
      };

      if (testApiKey) {
        // Set API key in service
        llmService.setApiKey(selectedProvider as any, testApiKey);
        addResult(`🔑 API key set for ${selectedProvider}`);

        // Try to execute the agent
        addResult('🚀 Executing agent task...');
        const response = await agent.execute(context);

        if (response.success) {
          addResult('✅ Agent execution successful!');
          addResult(`📝 Response: ${response.result.substring(0, 100)}...`);
          addResult(`🔢 Tokens used: ${response.tokensUsed}`);
        } else {
          addResult(`❌ Agent execution failed: ${response.error}`);
        }
      } else {
        addResult('⚠️ No API key provided - skipping actual execution');
      }

    } catch (error) {
      addResult(`❌ Error testing agent execution: ${error instanceof Error ? error.message : String(error)}`);
    }

    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>LLM Integration Test Suite</CardTitle>
        <CardDescription>
          Test the Multi-LLM Provider Integration to verify CORS fix and functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Configuration */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Provider</Label>
            <select
              value={selectedProvider}
              onChange={(e) => setSelectedProvider(e.target.value)}
              className="w-full p-2 border rounded"
            >
              {getAllProviders().map(provider => (
                <option key={provider} value={provider}>
                  {provider.charAt(0).toUpperCase() + provider.slice(1)}
                </option>
              ))}
            </select>
          </div>
          <div className="space-y-2">
            <Label>Test API Key (Optional)</Label>
            <Input
              type="password"
              placeholder="Enter API key for testing"
              value={testApiKey}
              onChange={(e) => setTestApiKey(e.target.value)}
            />
          </div>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={testElectronAPI}
            disabled={isLoading}
            variant="outline"
          >
            Test Electron API
          </Button>
          <Button
            onClick={testLLMService}
            disabled={isLoading}
            variant="outline"
          >
            Test LLM Service
          </Button>
          <Button
            onClick={testAgentExecution}
            disabled={isLoading}
            variant="outline"
          >
            Test Agent Execution
          </Button>
          <Button
            onClick={clearResults}
            disabled={isLoading}
            variant="secondary"
          >
            Clear Results
          </Button>
        </div>

        {/* Status */}
        <div className="flex items-center gap-2">
          <Badge variant={isLoading ? "secondary" : "default"}>
            {isLoading ? "Testing..." : "Ready"}
          </Badge>
          {typeof window !== 'undefined' && window.electronAPI?.llm && (
            <Badge variant="success">Electron API Available</Badge>
          )}
        </div>

        {/* Test Results */}
        <div className="space-y-2">
          <Label>Test Results</Label>
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index} className="whitespace-pre-wrap">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LLMTestComponent;
