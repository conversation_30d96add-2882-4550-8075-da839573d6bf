// components/agents/model-pricing-footer.tsx
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { DollarSign, TrendingUp, TrendingDown, Minus, Hash, Clock } from 'lucide-react';
import { getModelMetadata, formatContextSize } from './model-metadata';
import { LLMProvider } from './llm-provider-registry';

interface ModelPricingFooterProps {
  provider: LLMProvider;
  modelId: string;
  className?: string;
  showComparison?: boolean;
  comparisonModel?: { provider: LLMProvider; modelId: string };
}

export const ModelPricingFooter: React.FC<ModelPricingFooterProps> = ({
  provider,
  modelId,
  className = '',
  showComparison = false,
  comparisonModel
}) => {
  const metadata = getModelMetadata(provider, modelId);
  const comparisonMetadata = comparisonModel
    ? getModelMetadata(comparisonModel.provider, comparisonModel.modelId)
    : null;

  if (!metadata) {
    return (
      <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
        <DollarSign className="h-4 w-4" />
        <span>Pricing information not available</span>
      </div>
    );
  }

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return `$${price.toFixed(2)}`;
    } else if (price >= 0.001) {
      return `$${price.toFixed(3)}`;
    } else {
      return `$${price.toFixed(4)}`;
    }
  };

  const getPriceComparison = (current: number, comparison: number) => {
    const ratio = current / comparison;
    if (ratio > 1.1) {
      return { icon: TrendingUp, color: 'text-red-500', text: `${(ratio * 100 - 100).toFixed(0)}% more` };
    } else if (ratio < 0.9) {
      return { icon: TrendingDown, color: 'text-green-500', text: `${(100 - ratio * 100).toFixed(0)}% less` };
    } else {
      return { icon: Minus, color: 'text-muted-foreground', text: 'similar' };
    }
  };

  const getCostEfficiencyBadge = (pricing: { input: number; output: number }) => {
    const avgCost = (pricing.input + pricing.output) / 2;
    if (avgCost < 0.001) {
      return { variant: 'default' as const, text: 'Very Low Cost' };
    } else if (avgCost < 0.005) {
      return { variant: 'secondary' as const, text: 'Low Cost' };
    } else if (avgCost < 0.02) {
      return { variant: 'outline' as const, text: 'Moderate Cost' };
    } else {
      return { variant: 'destructive' as const, text: 'High Cost' };
    }
  };

  const costBadge = getCostEfficiencyBadge(metadata.pricing);

  return (
    <div className={`p-3 bg-muted/50 rounded-lg border ${className}`}>
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-4">
          <span className="font-medium">{metadata.label}</span>
          <span className="text-muted-foreground">•</span>
          <span>{formatContextSize(metadata.contextSize)}</span>
          <span className="text-muted-foreground">•</span>
          <span>{formatPrice(metadata.pricing.input)} / {formatPrice(metadata.pricing.output)} per 1K tokens</span>
        </div>
        <Badge variant={costBadge.variant} className="text-xs">
          {costBadge.text}
        </Badge>
      </div>

      {/* Capability Tags - Only show top 3 */}
      {metadata.tags && metadata.tags.length > 0 && (
        <div className="flex items-center gap-1 mt-2">
          {metadata.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {metadata.tags.length > 3 && (
            <span className="text-xs text-muted-foreground">+{metadata.tags.length - 3}</span>
          )}
        </div>
      )}

      {/* Comparison Display */}
      {showComparison && comparisonMetadata && (
        <div className="border-t pt-2 space-y-1">
          <div className="text-xs font-medium text-muted-foreground">
            Compared to {comparisonMetadata.label}:
          </div>
          <div className="flex items-center gap-4 text-xs">
            <div className="flex items-center gap-1">
              {(() => {
                const inputComp = getPriceComparison(metadata.pricing.input, comparisonMetadata.pricing.input);
                const InputIcon = inputComp.icon;
                return (
                  <>
                    <span>Input:</span>
                    <InputIcon className={`h-3 w-3 ${inputComp.color}`} />
                    <span className={inputComp.color}>{inputComp.text}</span>
                  </>
                );
              })()}
            </div>
            <div className="flex items-center gap-1">
              {(() => {
                const outputComp = getPriceComparison(metadata.pricing.output, comparisonMetadata.pricing.output);
                const OutputIcon = outputComp.icon;
                return (
                  <>
                    <span>Output:</span>
                    <OutputIcon className={`h-3 w-3 ${outputComp.color}`} />
                    <span className={outputComp.color}>{outputComp.text}</span>
                  </>
                );
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Cost Estimation Helper */}
      <div className="text-xs text-muted-foreground">
        <span>💡 Tip: For a typical 1K input + 1K output request, this costs approximately </span>
        <span className="font-medium">
          {formatPrice(metadata.pricing.input + metadata.pricing.output)}
        </span>
      </div>
    </div>
  );
};

export default ModelPricingFooter;
