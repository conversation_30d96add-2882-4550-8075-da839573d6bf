// components/agents/test-llm-integration.ts
import { LLMRequestService } from './llm-request-service';
import { getAllProviders, getProviderConfig } from './llm-provider-registry';
import { llmIntegration } from './llm-integration-service';
import { InternAgent } from './implementation/intern-agent';

/**
 * Test suite for LLM Integration
 * This file contains tests to verify the Multi-LLM Provider Integration works correctly
 */

export class LLMIntegrationTest {
  private llmService: LLMRequestService;

  constructor() {
    this.llmService = LLMRequestService.getInstance();
  }

  /**
   * Test provider registry functionality
   */
  public testProviderRegistry(): void {
    console.log('🧪 Testing Provider Registry...');
    
    const providers = getAllProviders();
    console.log(`✅ Found ${providers.length} providers:`, providers);

    providers.forEach(provider => {
      const config = getProviderConfig(provider);
      console.log(`✅ ${provider}:`, {
        name: config.name,
        modelCount: Object.keys(config.modelMap).length,
        costPer1k: config.costPer1kTokens,
        hasValidation: !!config.keyValidationEndpoint
      });
    });
  }

  /**
   * Test API key validation (without actual keys)
   */
  public async testApiKeyValidation(): Promise<void> {
    console.log('🧪 Testing API Key Validation...');
    
    // Test with empty key (should return false)
    try {
      const isValid = await this.llmService.validateApiKey('openai', '');
      console.log(`✅ Empty key validation: ${isValid ? '❌ FAIL' : '✅ PASS'}`);
    } catch (error) {
      console.log('✅ Empty key validation: ✅ PASS (threw error as expected)');
    }

    // Test with fake key (should return false)
    try {
      const isValid = await this.llmService.validateApiKey('openai', 'fake-key-123');
      console.log(`✅ Fake key validation: ${isValid ? '❌ FAIL' : '✅ PASS'}`);
    } catch (error) {
      console.log('✅ Fake key validation: ✅ PASS (threw error as expected)');
    }
  }

  /**
   * Test LLM integration service initialization
   */
  public async testIntegrationService(): Promise<void> {
    console.log('🧪 Testing LLM Integration Service...');
    
    try {
      await llmIntegration.initialize();
      console.log('✅ LLM Integration Service initialized successfully');
      
      const isInitialized = llmIntegration.isInitialized();
      console.log(`✅ Initialization status: ${isInitialized ? '✅ PASS' : '❌ FAIL'}`);
      
      const settingsManager = llmIntegration.getSettingsManager();
      const settings = settingsManager.getSettings();
      console.log(`✅ Settings loaded: ${settings.agents.length} agents configured`);
      
    } catch (error) {
      console.error('❌ LLM Integration Service test failed:', error);
    }
  }

  /**
   * Test agent configuration with providers
   */
  public testAgentConfiguration(): void {
    console.log('🧪 Testing Agent Configuration...');
    
    const settingsManager = llmIntegration.getSettingsManager();
    const settings = settingsManager.getSettings();
    
    settings.agents.forEach(agent => {
      console.log(`✅ ${agent.name}:`, {
        provider: agent.provider,
        model: agent.model,
        maxTokens: agent.maxTokens,
        temperature: agent.temperature
      });
    });
  }

  /**
   * Test agent creation with real LLM integration
   */
  public testAgentCreation(): void {
    console.log('🧪 Testing Agent Creation...');
    
    try {
      const internConfig = {
        id: 'intern',
        name: 'Test Intern',
        type: 'implementation',
        model: 'gpt-3.5-turbo',
        provider: 'openai' as const,
        maxTokens: 2000,
        temperature: 0.3
      };
      
      const internAgent = new InternAgent(internConfig);
      console.log('✅ InternAgent created successfully');
      console.log(`✅ Agent capabilities: ${internAgent.getCapabilities().join(', ')}`);
      
    } catch (error) {
      console.error('❌ Agent creation test failed:', error);
    }
  }

  /**
   * Test mock LLM call (without real API key)
   */
  public async testMockLLMCall(): Promise<void> {
    console.log('🧪 Testing Mock LLM Call...');
    
    try {
      // This will fail without a real API key, but we can test the structure
      const internConfig = {
        id: 'intern',
        name: 'Test Intern',
        type: 'implementation',
        model: 'gpt-3.5-turbo',
        provider: 'openai' as const,
        maxTokens: 2000,
        temperature: 0.3
      };
      
      const internAgent = new InternAgent(internConfig);
      const context = {
        task: 'Create a simple hello world function',
        files: [],
        codeContext: '',
        rules: ['Keep it simple', 'Use TypeScript'],
        dependencies: []
      };
      
      // This will fail due to no API key, but we can verify the structure
      try {
        await internAgent.execute(context);
        console.log('✅ LLM call structure test: ✅ PASS (unexpected success)');
      } catch (error) {
        if (error instanceof Error && error.message.includes('API key')) {
          console.log('✅ LLM call structure test: ✅ PASS (failed as expected - no API key)');
        } else {
          console.log('✅ LLM call structure test: ❌ FAIL (unexpected error):', error);
        }
      }
      
    } catch (error) {
      console.error('❌ Mock LLM call test failed:', error);
    }
  }

  /**
   * Run all tests
   */
  public async runAllTests(): Promise<void> {
    console.log('🚀 Starting LLM Integration Tests...\n');
    
    this.testProviderRegistry();
    console.log('');
    
    await this.testApiKeyValidation();
    console.log('');
    
    await this.testIntegrationService();
    console.log('');
    
    this.testAgentConfiguration();
    console.log('');
    
    this.testAgentCreation();
    console.log('');
    
    await this.testMockLLMCall();
    console.log('');
    
    console.log('✅ All LLM Integration Tests Completed!');
  }
}

// Export test runner function
export async function runLLMIntegrationTests(): Promise<void> {
  const tester = new LLMIntegrationTest();
  await tester.runAllTests();
}

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined' && (window as any).runLLMTests) {
  runLLMIntegrationTests().catch(console.error);
}
