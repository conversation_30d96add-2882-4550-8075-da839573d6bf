// components/background/code-indexer.ts
import { BasicVectorDatabase, VectorDocument } from './vector-database';

export interface IndexingOptions {
  includeExtensions?: string[];
  excludeExtensions?: string[];
  excludeDirectories?: string[];
  maxFileSize?: number; // in bytes
  chunkSize?: number; // for large files
}

export interface IndexingProgress {
  totalFiles: number;
  processedFiles: number;
  currentFile: string;
  errors: string[];
  isComplete: boolean;
}

export interface FileChunk {
  id: string;
  filePath: string;
  content: string;
  chunkIndex: number;
  totalChunks: number;
  startLine: number;
  endLine: number;
}

export class CodeIndexer {
  private vectorDb: BasicVectorDatabase;
  private isIndexing = false;
  private progress: IndexingProgress = {
    totalFiles: 0,
    processedFiles: 0,
    currentFile: '',
    errors: [],
    isComplete: false
  };

  // Default indexing options
  private defaultOptions: IndexingOptions = {
    includeExtensions: [
      '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
      '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
      '.html', '.css', '.scss', '.less', '.vue', '.svelte',
      '.json', '.xml', '.yaml', '.yml', '.md', '.txt'
    ],
    excludeDirectories: [
      'node_modules', '.git', '.next', 'dist', 'build', 'coverage',
      '.vscode', '.idea', 'target', 'bin', 'obj', '__pycache__'
    ],
    maxFileSize: 1024 * 1024, // 1MB
    chunkSize: 2000 // characters per chunk
  };

  constructor(vectorDatabase: BasicVectorDatabase) {
    this.vectorDb = vectorDatabase;
  }

  /**
   * Index a project directory
   */
  async indexProject(projectPath: string, options: IndexingOptions = {}): Promise<IndexingProgress> {
    if (this.isIndexing) {
      throw new Error('Indexing already in progress');
    }

    this.isIndexing = true;
    this.resetProgress();

    const finalOptions = { ...this.defaultOptions, ...options };

    try {
      console.log(`Starting indexing of project: ${projectPath}`);
      
      // Get all files to index
      const filesToIndex = await this.getFilesToIndex(projectPath, finalOptions);
      this.progress.totalFiles = filesToIndex.length;

      // Process each file
      for (const filePath of filesToIndex) {
        try {
          this.progress.currentFile = filePath;
          await this.indexFile(filePath, finalOptions);
          this.progress.processedFiles++;
        } catch (error) {
          const errorMsg = `Failed to index ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          this.progress.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      this.progress.isComplete = true;
      console.log(`Indexing complete. Processed ${this.progress.processedFiles}/${this.progress.totalFiles} files`);
      
    } catch (error) {
      console.error('Indexing failed:', error);
      this.progress.errors.push(`Indexing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      this.isIndexing = false;
    }

    return this.progress;
  }

  /**
   * Index a single file
   */
  async indexFile(filePath: string, options: IndexingOptions = {}): Promise<void> {
    try {
      const content = await this.readFileContent(filePath);
      const fileInfo = this.getFileInfo(filePath);
      
      // Check file size
      if (content.length > (options.maxFileSize || this.defaultOptions.maxFileSize!)) {
        console.warn(`Skipping large file: ${filePath} (${content.length} bytes)`);
        return;
      }

      // Split large files into chunks
      const chunks = this.chunkContent(content, options.chunkSize || this.defaultOptions.chunkSize!);
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const documentId = chunks.length > 1 ? `${filePath}#chunk-${i}` : filePath;
        
        const document: Omit<VectorDocument, 'vector' | 'embedding'> = {
          id: documentId,
          content: chunk.content,
          metadata: {
            filePath: filePath,
            fileType: fileInfo.extension,
            language: this.detectLanguage(fileInfo.extension),
            timestamp: Date.now(),
            size: chunk.content.length
          }
        };

        await this.vectorDb.addDocument(document);
      }

      console.log(`Indexed file: ${filePath} (${chunks.length} chunks)`);
    } catch (error) {
      console.error(`Failed to index file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get indexing progress
   */
  getProgress(): IndexingProgress {
    return { ...this.progress };
  }

  /**
   * Check if indexing is in progress
   */
  isIndexingInProgress(): boolean {
    return this.isIndexing;
  }

  /**
   * Cancel ongoing indexing
   */
  cancelIndexing(): void {
    this.isIndexing = false;
    console.log('Indexing cancelled');
  }

  /**
   * Re-index specific files (for incremental updates)
   */
  async reindexFiles(filePaths: string[], options: IndexingOptions = {}): Promise<void> {
    for (const filePath of filePaths) {
      try {
        // Remove existing entries for this file
        await this.removeFileFromIndex(filePath);
        
        // Re-index the file
        await this.indexFile(filePath, options);
      } catch (error) {
        console.error(`Failed to re-index ${filePath}:`, error);
      }
    }
  }

  /**
   * Remove a file from the index
   */
  async removeFileFromIndex(filePath: string): Promise<void> {
    try {
      const allIds = this.vectorDb.getAllDocumentIds();
      const fileIds = allIds.filter(id => id.startsWith(filePath));
      
      for (const id of fileIds) {
        await this.vectorDb.removeDocument(id);
      }
      
      console.log(`Removed ${fileIds.length} entries for file: ${filePath}`);
    } catch (error) {
      console.error(`Failed to remove file from index: ${filePath}`, error);
    }
  }

  /**
   * Get files to index based on options
   */
  private async getFilesToIndex(projectPath: string, options: IndexingOptions): Promise<string[]> {
    // This is a mock implementation - in a real scenario, you'd traverse the file system
    // For now, return a sample list of files
    const mockFiles = [
      `${projectPath}/src/index.ts`,
      `${projectPath}/src/components/App.tsx`,
      `${projectPath}/src/utils/helpers.js`,
      `${projectPath}/README.md`,
      `${projectPath}/package.json`
    ];

    return mockFiles.filter(filePath => {
      const fileInfo = this.getFileInfo(filePath);
      
      // Check include extensions
      if (options.includeExtensions && !options.includeExtensions.includes(fileInfo.extension)) {
        return false;
      }
      
      // Check exclude extensions
      if (options.excludeExtensions && options.excludeExtensions.includes(fileInfo.extension)) {
        return false;
      }
      
      // Check exclude directories
      if (options.excludeDirectories) {
        for (const excludeDir of options.excludeDirectories) {
          if (filePath.includes(`/${excludeDir}/`) || filePath.includes(`\\${excludeDir}\\`)) {
            return false;
          }
        }
      }
      
      return true;
    });
  }

  /**
   * Read file content (mock implementation)
   */
  private async readFileContent(filePath: string): Promise<string> {
    // Mock file content based on file type
    const fileInfo = this.getFileInfo(filePath);
    
    switch (fileInfo.extension) {
      case '.ts':
      case '.tsx':
        return `// TypeScript file: ${filePath}\nexport interface Example {\n  id: string;\n  name: string;\n}\n\nexport function processData(data: Example[]): void {\n  console.log('Processing data:', data);\n}`;
      
      case '.js':
      case '.jsx':
        return `// JavaScript file: ${filePath}\nfunction calculateSum(a, b) {\n  return a + b;\n}\n\nmodule.exports = { calculateSum };`;
      
      case '.md':
        return `# ${fileInfo.name}\n\nThis is a markdown file with documentation.\n\n## Features\n\n- Feature 1\n- Feature 2\n- Feature 3`;
      
      case '.json':
        return `{\n  "name": "${fileInfo.name}",\n  "version": "1.0.0",\n  "description": "Sample JSON file"\n}`;
      
      default:
        return `Content of ${filePath}\n\nThis is sample content for indexing purposes.`;
    }
  }

  /**
   * Get file information from path
   */
  private getFileInfo(filePath: string): { name: string; extension: string; directory: string } {
    const parts = filePath.split(/[/\\]/);
    const fileName = parts[parts.length - 1];
    const dotIndex = fileName.lastIndexOf('.');
    
    return {
      name: dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName,
      extension: dotIndex > 0 ? fileName.substring(dotIndex) : '',
      directory: parts.slice(0, -1).join('/')
    };
  }

  /**
   * Detect programming language from file extension
   */
  private detectLanguage(extension: string): string {
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.less': 'less',
      '.vue': 'vue',
      '.svelte': 'svelte',
      '.json': 'json',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.md': 'markdown',
      '.txt': 'text'
    };

    return languageMap[extension.toLowerCase()] || 'unknown';
  }

  /**
   * Split content into chunks
   */
  private chunkContent(content: string, chunkSize: number): FileChunk[] {
    if (content.length <= chunkSize) {
      return [{
        id: '0',
        filePath: '',
        content,
        chunkIndex: 0,
        totalChunks: 1,
        startLine: 1,
        endLine: content.split('\n').length
      }];
    }

    const chunks: FileChunk[] = [];
    const lines = content.split('\n');
    let currentChunk = '';
    let startLine = 1;
    let chunkIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i] + '\n';
      
      if (currentChunk.length + line.length > chunkSize && currentChunk.length > 0) {
        chunks.push({
          id: chunkIndex.toString(),
          filePath: '',
          content: currentChunk.trim(),
          chunkIndex,
          totalChunks: 0, // Will be set later
          startLine,
          endLine: i
        });
        
        currentChunk = line;
        startLine = i + 1;
        chunkIndex++;
      } else {
        currentChunk += line;
      }
    }

    // Add the last chunk
    if (currentChunk.trim().length > 0) {
      chunks.push({
        id: chunkIndex.toString(),
        filePath: '',
        content: currentChunk.trim(),
        chunkIndex,
        totalChunks: 0,
        startLine,
        endLine: lines.length
      });
    }

    // Set total chunks for all chunks
    chunks.forEach(chunk => {
      chunk.totalChunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Reset progress tracking
   */
  private resetProgress(): void {
    this.progress = {
      totalFiles: 0,
      processedFiles: 0,
      currentFile: '',
      errors: [],
      isComplete: false
    };
  }
}
