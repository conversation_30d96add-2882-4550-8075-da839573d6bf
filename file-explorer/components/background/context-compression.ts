// components/background/context-compression.ts
import { ContextPrefetcher, PrefetchedContext, ContextItem } from './context-prefetcher';
import { ContextRelevanceScorer, ScoringResult } from './context-relevance-scorer';
import { ContextCacheManager } from './context-cache-manager';
import { BasicVectorDatabase, VectorDocument } from './vector-database';
import { getConfigStoreBrowser } from './config-store-browser';

export interface CompressionRequest {
  id: string;
  contexts: ContextItem[];
  targetTokens: number;
  maxTokens: number;
  compressionLevel: 'light' | 'medium' | 'aggressive' | 'extreme';
  preserveStructure: boolean;
  preserveImports: boolean;
  preserveComments: boolean;
  preserveTypes: boolean;
  priority: 'speed' | 'quality' | 'balanced';
  agentType?: string;
  taskType?: string;
  metadata?: Record<string, any>;
}

export interface CompressedContext {
  id: string;
  requestId: string;
  originalTokens: number;
  compressedTokens: number;
  compressionRatio: number;
  contexts: CompressedContextItem[];
  summary: ContextSummary;
  hierarchy: ContextHierarchy;
  preservedElements: PreservedElements;
  compressionMetrics: CompressionMetrics;
  generatedAt: number;
  expiresAt: number;
}

export interface CompressedContextItem {
  id: string;
  originalId: string;
  type: ContextItem['type'];
  source: string;
  compressedContent: string;
  originalTokens: number;
  compressedTokens: number;
  compressionRatio: number;
  relevanceScore: number;
  preservedElements: string[];
  compressionTechniques: CompressionTechnique[];
  metadata: {
    language?: string;
    symbols?: string[];
    keyElements?: string[];
    relationships?: string[];
    importance: number;
  };
}

export interface ContextSummary {
  overview: string;
  keyPoints: string[];
  mainConcepts: string[];
  criticalElements: string[];
  relationships: string[];
  dependencies: string[];
  patterns: string[];
  recommendations: string[];
  tokenCount: number;
}

export interface ContextHierarchy {
  levels: HierarchyLevel[];
  totalLevels: number;
  loadingStrategy: 'progressive' | 'priority' | 'adaptive';
  levelThresholds: number[];
}

export interface HierarchyLevel {
  level: number;
  name: string;
  description: string;
  contexts: string[]; // context IDs
  tokenCount: number;
  importance: number;
  dependencies: string[]; // other level IDs
  loadPriority: number;
}

export interface PreservedElements {
  imports: string[];
  exports: string[];
  types: string[];
  interfaces: string[];
  functions: string[];
  classes: string[];
  constants: string[];
  comments: string[];
  documentation: string[];
}

export interface CompressionMetrics {
  totalOriginalTokens: number;
  totalCompressedTokens: number;
  overallCompressionRatio: number;
  compressionTime: number;
  qualityScore: number; // 0-1, how well compression preserved meaning
  informationLoss: number; // 0-1, estimated information loss
  techniques: {
    [technique: string]: {
      applied: number;
      tokensReduced: number;
      effectiveness: number;
    };
  };
}

export type CompressionTechnique =
  | 'whitespace_removal'
  | 'comment_removal'
  | 'variable_shortening'
  | 'redundancy_elimination'
  | 'code_summarization'
  | 'pattern_abstraction'
  | 'dependency_pruning'
  | 'semantic_compression'
  | 'hierarchical_structuring'
  | 'token_optimization';

export interface TokenOptimization {
  strategy: 'greedy' | 'dynamic' | 'semantic' | 'hybrid';
  windowSize: number;
  overlapSize: number;
  priorityWeights: {
    relevance: number;
    recency: number;
    importance: number;
    dependencies: number;
  };
  adaptiveThresholds: boolean;
  contextAware: boolean;
}

export interface CompressionConfig {
  defaultCompressionLevel: CompressionRequest['compressionLevel'];
  maxCompressionRatio: number; // 0-1, max allowed compression
  minQualityScore: number; // 0-1, minimum acceptable quality
  enableSemanticCompression: boolean;
  enableHierarchicalLoading: boolean;
  enableTokenOptimization: boolean;
  cacheCompressedContexts: boolean;
  compressionTimeout: number; // milliseconds
  maxConcurrentCompressions: number;
  preservationRules: {
    alwaysPreserve: string[]; // patterns to always preserve
    neverCompress: string[]; // patterns to never compress
    criticalElements: string[]; // elements marked as critical
  };
  tokenOptimization: TokenOptimization;
}

export interface CompressionStats {
  totalCompressions: number;
  averageCompressionRatio: number;
  averageQualityScore: number;
  averageProcessingTime: number;
  totalTokensReduced: number;
  cacheHitRate: number;
  techniqueEffectiveness: {
    [technique: string]: {
      usageCount: number;
      averageReduction: number;
      averageQuality: number;
    };
  };
}

export class ContextCompression {
  private contextPrefetcher: ContextPrefetcher;
  private relevanceScorer: ContextRelevanceScorer;
  private cacheManager: ContextCacheManager;
  private vectorDatabase: BasicVectorDatabase;
  private configStore: any;

  private compressionCache: Map<string, CompressedContext> = new Map();
  private activeCompressions: Map<string, CompressionRequest> = new Map();
  private processingQueue: CompressionRequest[] = [];

  private config: CompressionConfig = {
    defaultCompressionLevel: 'medium',
    maxCompressionRatio: 0.8,
    minQualityScore: 0.7,
    enableSemanticCompression: true,
    enableHierarchicalLoading: true,
    enableTokenOptimization: true,
    cacheCompressedContexts: true,
    compressionTimeout: 30000,
    maxConcurrentCompressions: 3,
    preservationRules: {
      alwaysPreserve: ['export', 'import', 'interface', 'type', 'class'],
      neverCompress: ['TODO', 'FIXME', 'NOTE', 'WARNING'],
      criticalElements: ['main', 'index', 'config', 'types']
    },
    tokenOptimization: {
      strategy: 'hybrid',
      windowSize: 4000,
      overlapSize: 200,
      priorityWeights: {
        relevance: 0.4,
        recency: 0.2,
        importance: 0.3,
        dependencies: 0.1
      },
      adaptiveThresholds: true,
      contextAware: true
    }
  };

  private stats: CompressionStats = {
    totalCompressions: 0,
    averageCompressionRatio: 0,
    averageQualityScore: 0,
    averageProcessingTime: 0,
    totalTokensReduced: 0,
    cacheHitRate: 0,
    techniqueEffectiveness: {}
  };

  constructor(private projectId: string) {
    this.contextPrefetcher = new ContextPrefetcher(projectId);
    this.relevanceScorer = new ContextRelevanceScorer(projectId);
    this.cacheManager = new ContextCacheManager();
    this.vectorDatabase = new BasicVectorDatabase();
    this.configStore = getConfigStoreBrowser();
  }

  /**
   * Initialize the context compression system
   */
  async initialize(): Promise<void> {
    try {
      await this.contextPrefetcher.initialize();
      await this.relevanceScorer.initialize();
      await this.cacheManager.initialize();
      await this.vectorDatabase.initialize();
      await this.loadConfiguration();

      console.log('Context compression system initialized');
    } catch (error) {
      console.error('Failed to initialize context compression system:', error);
      throw error;
    }
  }

  /**
   * Compress contexts to fit within token limits
   */
  async compressContexts(request: Omit<CompressionRequest, 'id'>): Promise<CompressedContext> {
    const requestId = this.generateRequestId();
    const fullRequest: CompressionRequest = {
      ...request,
      id: requestId
    };

    // Check cache first
    const cacheKey = this.generateCacheKey(fullRequest);
    if (this.config.cacheCompressedContexts && this.compressionCache.has(cacheKey)) {
      const cached = this.compressionCache.get(cacheKey)!;
      if (Date.now() < cached.expiresAt) {
        this.stats.cacheHitRate = (this.stats.cacheHitRate * this.stats.totalCompressions + 1) / (this.stats.totalCompressions + 1);
        return cached;
      }
    }

    const startTime = Date.now();

    try {
      // Validate request
      this.validateCompressionRequest(fullRequest);

      // Add to active compressions
      this.activeCompressions.set(requestId, fullRequest);

      // Calculate original token count
      const originalTokens = this.calculateTotalTokens(fullRequest.contexts);

      // Check if compression is needed
      if (originalTokens <= fullRequest.targetTokens) {
        return this.createUncompressedResult(fullRequest, originalTokens);
      }

      // Score contexts for relevance
      const scoringResults = await this.scoreContextsForCompression(fullRequest);

      // Apply compression techniques
      const compressedContexts = await this.applyCompressionTechniques(fullRequest, scoringResults);

      // Create context summary
      const summary = await this.createContextSummary(compressedContexts, fullRequest);

      // Build context hierarchy
      const hierarchy = this.buildContextHierarchy(compressedContexts, fullRequest);

      // Extract preserved elements
      const preservedElements = this.extractPreservedElements(compressedContexts);

      // Calculate compression metrics
      const compressionMetrics = this.calculateCompressionMetrics(
        fullRequest.contexts,
        compressedContexts,
        startTime
      );

      // Create compressed context result
      const result: CompressedContext = {
        id: this.generateResultId(),
        requestId,
        originalTokens,
        compressedTokens: compressionMetrics.totalCompressedTokens,
        compressionRatio: compressionMetrics.overallCompressionRatio,
        contexts: compressedContexts,
        summary,
        hierarchy,
        preservedElements,
        compressionMetrics,
        generatedAt: Date.now(),
        expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
      };

      // Cache result
      if (this.config.cacheCompressedContexts) {
        this.compressionCache.set(cacheKey, result);

        // Limit cache size
        if (this.compressionCache.size > 100) {
          const oldestKey = this.compressionCache.keys().next().value;
          this.compressionCache.delete(oldestKey);
        }
      }

      // Update statistics
      this.updateCompressionStats(compressionMetrics);

      // Clean up
      this.activeCompressions.delete(requestId);

      console.log(`Context compression completed: ${originalTokens} -> ${result.compressedTokens} tokens (${(result.compressionRatio * 100).toFixed(1)}% compression)`);
      return result;

    } catch (error) {
      this.activeCompressions.delete(requestId);
      console.error(`Context compression failed for request ${requestId}:`, error);
      throw error;
    }
  }

  /**
   * Optimize token usage for context windows
   */
  async optimizeTokenUsage(contexts: ContextItem[], maxTokens: number, strategy?: TokenOptimization['strategy']): Promise<ContextItem[]> {
    const optimizationStrategy = strategy || this.config.tokenOptimization.strategy;

    switch (optimizationStrategy) {
      case 'greedy':
        return this.greedyTokenOptimization(contexts, maxTokens);
      case 'dynamic':
        return this.dynamicTokenOptimization(contexts, maxTokens);
      case 'semantic':
        return this.semanticTokenOptimization(contexts, maxTokens);
      case 'hybrid':
        return this.hybridTokenOptimization(contexts, maxTokens);
      default:
        return this.greedyTokenOptimization(contexts, maxTokens);
    }
  }

  /**
   * Create hierarchical context loading strategy
   */
  createHierarchicalLoadingStrategy(contexts: ContextItem[], maxLevels: number = 5): ContextHierarchy {
    const levels: HierarchyLevel[] = [];
    const sortedContexts = [...contexts].sort((a, b) => b.relevanceScore - a.relevanceScore);

    const contextsPerLevel = Math.ceil(sortedContexts.length / maxLevels);

    for (let i = 0; i < maxLevels && i * contextsPerLevel < sortedContexts.length; i++) {
      const levelContexts = sortedContexts.slice(i * contextsPerLevel, (i + 1) * contextsPerLevel);
      const tokenCount = levelContexts.reduce((sum, ctx) => sum + ctx.tokens, 0);

      levels.push({
        level: i + 1,
        name: i === 0 ? 'Critical' : i === 1 ? 'Important' : i === 2 ? 'Relevant' : i === 3 ? 'Supporting' : 'Additional',
        description: this.generateLevelDescription(i + 1, levelContexts),
        contexts: levelContexts.map(ctx => ctx.id),
        tokenCount,
        importance: 1 - (i / maxLevels),
        dependencies: i > 0 ? [levels[i - 1].level.toString()] : [],
        loadPriority: maxLevels - i
      });
    }

    return {
      levels,
      totalLevels: levels.length,
      loadingStrategy: 'progressive',
      levelThresholds: levels.map(level => level.tokenCount)
    };
  }

  /**
   * Get compression statistics
   */
  getCompressionStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * Update compression configuration
   */
  async updateConfig(newConfig: Partial<CompressionConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    console.log('Context compression configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): CompressionConfig {
    return { ...this.config };
  }

  // Private implementation methods

  /**
   * Score contexts for compression prioritization
   */
  private async scoreContextsForCompression(request: CompressionRequest): Promise<ScoringResult[]> {
    // Create a mock prefetch request for scoring
    const mockPrefetchRequest = {
      id: request.id,
      taskType: request.taskType || 'compression',
      agentId: request.agentType || 'compression-agent',
      agentCapabilities: ['compression'],
      taskDescription: 'Context compression task',
      priority: 'normal' as const,
      requiredContext: [],
      optionalContext: [],
      maxContextSize: request.maxTokens
    };

    return await this.relevanceScorer.scoreContexts(request.contexts, mockPrefetchRequest);
  }

  /**
   * Apply compression techniques based on level and request
   */
  private async applyCompressionTechniques(request: CompressionRequest, scoringResults: ScoringResult[]): Promise<CompressedContextItem[]> {
    const compressedContexts: CompressedContextItem[] = [];
    const sortedResults = scoringResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

    let remainingTokens = request.targetTokens;

    for (const result of sortedResults) {
      const context = request.contexts.find(ctx => ctx.id === result.contextId);
      if (!context || remainingTokens <= 0) continue;

      const targetTokensForContext = Math.min(context.tokens, remainingTokens);
      const compressionRatio = targetTokensForContext / context.tokens;

      const compressedContext = await this.compressContextItem(
        context,
        compressionRatio,
        request.compressionLevel,
        request
      );

      if (compressedContext.compressedTokens <= remainingTokens) {
        compressedContexts.push(compressedContext);
        remainingTokens -= compressedContext.compressedTokens;
      }
    }

    return compressedContexts;
  }

  /**
   * Compress a single context item
   */
  private async compressContextItem(
    context: ContextItem,
    compressionRatio: number,
    level: CompressionRequest['compressionLevel'],
    request: CompressionRequest
  ): Promise<CompressedContextItem> {
    const techniques: CompressionTechnique[] = [];
    let compressedContent = context.content;
    let preservedElements: string[] = [];

    // Apply compression techniques based on level
    switch (level) {
      case 'light':
        compressedContent = this.applyLightCompression(compressedContent, techniques, preservedElements, request);
        break;
      case 'medium':
        compressedContent = this.applyMediumCompression(compressedContent, techniques, preservedElements, request);
        break;
      case 'aggressive':
        compressedContent = this.applyAggressiveCompression(compressedContent, techniques, preservedElements, request);
        break;
      case 'extreme':
        compressedContent = this.applyExtremeCompression(compressedContent, techniques, preservedElements, request);
        break;
    }

    const compressedTokens = this.estimateTokenCount(compressedContent);
    const actualCompressionRatio = compressedTokens / context.tokens;

    return {
      id: this.generateCompressedContextId(),
      originalId: context.id,
      type: context.type,
      source: context.source,
      compressedContent,
      originalTokens: context.tokens,
      compressedTokens,
      compressionRatio: actualCompressionRatio,
      relevanceScore: context.relevanceScore,
      preservedElements,
      compressionTechniques: techniques,
      metadata: {
        language: context.language,
        symbols: context.metadata.symbols,
        keyElements: this.extractKeyElements(compressedContent),
        relationships: this.extractRelationships(compressedContent),
        importance: context.relevanceScore
      }
    };
  }

  /**
   * Apply light compression (minimal changes)
   */
  private applyLightCompression(
    content: string,
    techniques: CompressionTechnique[],
    preservedElements: string[],
    request: CompressionRequest
  ): string {
    let compressed = content;

    // Remove extra whitespace
    compressed = compressed.replace(/\s+/g, ' ').trim();
    techniques.push('whitespace_removal');

    // Remove non-essential comments if allowed
    if (!request.preserveComments) {
      compressed = compressed.replace(/\/\*[\s\S]*?\*\//g, '');
      compressed = compressed.replace(/\/\/.*$/gm, '');
      techniques.push('comment_removal');
    } else {
      preservedElements.push('comments');
    }

    return compressed;
  }

  /**
   * Apply medium compression (moderate changes)
   */
  private applyMediumCompression(
    content: string,
    techniques: CompressionTechnique[],
    preservedElements: string[],
    request: CompressionRequest
  ): string {
    let compressed = this.applyLightCompression(content, techniques, preservedElements, request);

    // Remove redundant code
    compressed = this.removeRedundancy(compressed);
    techniques.push('redundancy_elimination');

    // Shorten variable names (carefully)
    if (!this.containsCriticalElements(compressed)) {
      compressed = this.shortenVariableNames(compressed);
      techniques.push('variable_shortening');
    }

    return compressed;
  }

  /**
   * Apply aggressive compression (significant changes)
   */
  private applyAggressiveCompression(
    content: string,
    techniques: CompressionTechnique[],
    preservedElements: string[],
    request: CompressionRequest
  ): string {
    let compressed = this.applyMediumCompression(content, techniques, preservedElements, request);

    // Apply code summarization
    compressed = this.summarizeCode(compressed);
    techniques.push('code_summarization');

    // Abstract patterns
    compressed = this.abstractPatterns(compressed);
    techniques.push('pattern_abstraction');

    return compressed;
  }

  /**
   * Apply extreme compression (maximum reduction)
   */
  private applyExtremeCompression(
    content: string,
    techniques: CompressionTechnique[],
    preservedElements: string[],
    request: CompressionRequest
  ): string {
    let compressed = this.applyAggressiveCompression(content, techniques, preservedElements, request);

    // Semantic compression
    if (this.config.enableSemanticCompression) {
      compressed = this.applySemanticCompression(compressed);
      techniques.push('semantic_compression');
    }

    // Prune dependencies
    compressed = this.pruneDependencies(compressed);
    techniques.push('dependency_pruning');

    return compressed;
  }

  // Token optimization strategies

  /**
   * Greedy token optimization (simple, fast)
   */
  private greedyTokenOptimization(contexts: ContextItem[], maxTokens: number): ContextItem[] {
    const sortedContexts = [...contexts].sort((a, b) => b.relevanceScore - a.relevanceScore);
    const result: ContextItem[] = [];
    let totalTokens = 0;

    for (const context of sortedContexts) {
      if (totalTokens + context.tokens <= maxTokens) {
        result.push(context);
        totalTokens += context.tokens;
      }
    }

    return result;
  }

  /**
   * Dynamic token optimization (adaptive)
   */
  private dynamicTokenOptimization(contexts: ContextItem[], maxTokens: number): ContextItem[] {
    const weights = this.config.tokenOptimization.priorityWeights;

    // Calculate dynamic scores
    const scoredContexts = contexts.map(context => ({
      context,
      score: (context.relevanceScore * weights.relevance) +
             (this.calculateRecencyScore(context) * weights.recency) +
             (this.calculateImportanceScore(context) * weights.importance) +
             (this.calculateDependencyScore(context) * weights.dependencies)
    }));

    // Sort by dynamic score
    scoredContexts.sort((a, b) => b.score - a.score);

    const result: ContextItem[] = [];
    let totalTokens = 0;

    for (const { context } of scoredContexts) {
      if (totalTokens + context.tokens <= maxTokens) {
        result.push(context);
        totalTokens += context.tokens;
      }
    }

    return result;
  }

  /**
   * Semantic token optimization (meaning-preserving)
   */
  private async semanticTokenOptimization(contexts: ContextItem[], maxTokens: number): Promise<ContextItem[]> {
    // Group contexts by semantic similarity
    const semanticGroups = await this.groupContextsBySemantic(contexts);

    const result: ContextItem[] = [];
    let totalTokens = 0;

    // Select representative contexts from each group
    for (const group of semanticGroups) {
      const representative = this.selectRepresentativeContext(group);
      if (totalTokens + representative.tokens <= maxTokens) {
        result.push(representative);
        totalTokens += representative.tokens;
      }
    }

    return result;
  }

  /**
   * Hybrid token optimization (combines multiple strategies)
   */
  private async hybridTokenOptimization(contexts: ContextItem[], maxTokens: number): Promise<ContextItem[]> {
    // Apply multiple strategies and combine results
    const greedyResult = this.greedyTokenOptimization(contexts, maxTokens);
    const dynamicResult = this.dynamicTokenOptimization(contexts, maxTokens);
    const semanticResult = await this.semanticTokenOptimization(contexts, maxTokens);

    // Score each result set
    const greedyScore = this.scoreOptimizationResult(greedyResult);
    const dynamicScore = this.scoreOptimizationResult(dynamicResult);
    const semanticScore = this.scoreOptimizationResult(semanticResult);

    // Return the best result
    if (greedyScore >= dynamicScore && greedyScore >= semanticScore) {
      return greedyResult;
    } else if (dynamicScore >= semanticScore) {
      return dynamicResult;
    } else {
      return semanticResult;
    }
  }

  // Compression helper methods

  /**
   * Remove redundant code patterns
   */
  private removeRedundancy(content: string): string {
    let compressed = content;

    // Remove duplicate imports
    const imports = content.match(/import\s+.*?from\s+['"][^'"]+['"];?/g) || [];
    const uniqueImports = [...new Set(imports)];
    if (imports.length > uniqueImports.length) {
      compressed = compressed.replace(/import\s+.*?from\s+['"][^'"]+['"];?\s*/g, '');
      compressed = uniqueImports.join('\n') + '\n' + compressed;
    }

    // Remove duplicate function declarations (simplified)
    const functions = content.match(/function\s+\w+\s*\([^)]*\)\s*{[^}]*}/g) || [];
    const uniqueFunctions = [...new Set(functions)];
    if (functions.length > uniqueFunctions.length) {
      // Replace duplicates with references
      for (let i = 1; i < functions.length; i++) {
        if (functions.slice(0, i).includes(functions[i])) {
          compressed = compressed.replace(functions[i], '// Duplicate function removed');
        }
      }
    }

    return compressed;
  }

  /**
   * Shorten variable names while preserving meaning
   */
  private shortenVariableNames(content: string): string {
    let compressed = content;
    const variableMap = new Map<string, string>();
    let counter = 1;

    // Find long variable names (>8 characters)
    const longVariables = content.match(/\b[a-zA-Z_][a-zA-Z0-9_]{8,}\b/g) || [];
    const uniqueLongVariables = [...new Set(longVariables)];

    for (const variable of uniqueLongVariables) {
      // Skip if it's a preserved element
      if (this.config.preservationRules.alwaysPreserve.some(pattern => variable.includes(pattern))) {
        continue;
      }

      // Generate short name
      const shortName = this.generateShortVariableName(variable, counter++);
      variableMap.set(variable, shortName);

      // Replace all occurrences
      const regex = new RegExp(`\\b${variable}\\b`, 'g');
      compressed = compressed.replace(regex, shortName);
    }

    return compressed;
  }

  /**
   * Summarize code blocks
   */
  private summarizeCode(content: string): string {
    let compressed = content;

    // Summarize long function bodies
    const functionPattern = /(function\s+\w+\s*\([^)]*\)\s*{)([\s\S]*?)(})/g;
    compressed = compressed.replace(functionPattern, (match, start, body, end) => {
      if (body.length > 200) {
        const summary = this.createCodeSummary(body);
        return `${start}\n  // Function body summarized: ${summary}\n  // Original length: ${body.length} chars\n${end}`;
      }
      return match;
    });

    return compressed;
  }

  /**
   * Abstract common patterns
   */
  private abstractPatterns(content: string): string {
    let compressed = content;

    // Abstract repetitive patterns
    const patterns = [
      { pattern: /console\.log\([^)]+\);?\s*/g, replacement: '// Debug logging removed\n' },
      { pattern: /\/\*\s*TODO:.*?\*\//g, replacement: '// TODO items removed' },
      { pattern: /\/\*\s*FIXME:.*?\*\//g, replacement: '// FIXME items removed' }
    ];

    for (const { pattern, replacement } of patterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 3) {
        compressed = compressed.replace(pattern, replacement);
      }
    }

    return compressed;
  }

  /**
   * Apply semantic compression using vector similarity
   */
  private applySemanticCompression(content: string): string {
    // This is a simplified semantic compression
    // In a real implementation, this would use the vector database
    // to find semantically similar content and compress it

    const lines = content.split('\n');
    const compressedLines: string[] = [];
    const seenConcepts = new Set<string>();

    for (const line of lines) {
      const concept = this.extractLineConcept(line);
      if (concept && seenConcepts.has(concept)) {
        compressedLines.push(`// Similar to previous: ${concept}`);
      } else {
        compressedLines.push(line);
        if (concept) seenConcepts.add(concept);
      }
    }

    return compressedLines.join('\n');
  }

  /**
   * Prune unnecessary dependencies
   */
  private pruneDependencies(content: string): string {
    let compressed = content;

    // Find unused imports
    const imports = content.match(/import\s+({[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g) || [];
    const usedImports: string[] = [];

    for (const importStatement of imports) {
      const match = importStatement.match(/import\s+({[^}]+}|\w+|\*\s+as\s+\w+)\s+from/);
      if (match) {
        const importedItems = match[1];

        // Check if any imported item is used in the code
        if (this.isImportUsed(importedItems, content)) {
          usedImports.push(importStatement);
        }
      }
    }

    // Replace imports with only used ones
    if (usedImports.length < imports.length) {
      compressed = compressed.replace(/import\s+.*?from\s+['"][^'"]+['"];?\s*/g, '');
      compressed = usedImports.join('\n') + '\n' + compressed;
    }

    return compressed;
  }

  // Utility methods

  /**
   * Create context summary
   */
  private async createContextSummary(contexts: CompressedContextItem[], request: CompressionRequest): Promise<ContextSummary> {
    const allContent = contexts.map(ctx => ctx.compressedContent).join('\n');

    return {
      overview: this.generateOverview(contexts),
      keyPoints: this.extractKeyPoints(contexts),
      mainConcepts: this.extractMainConcepts(contexts),
      criticalElements: this.extractCriticalElements(contexts),
      relationships: this.extractRelationships(allContent),
      dependencies: this.extractDependencies(contexts),
      patterns: this.extractPatterns(contexts),
      recommendations: this.generateRecommendations(contexts),
      tokenCount: contexts.reduce((sum, ctx) => sum + ctx.compressedTokens, 0)
    };
  }

  /**
   * Build context hierarchy
   */
  private buildContextHierarchy(contexts: CompressedContextItem[], request: CompressionRequest): ContextHierarchy {
    const levels: HierarchyLevel[] = [];
    const sortedContexts = [...contexts].sort((a, b) => b.relevanceScore - a.relevanceScore);

    const maxLevels = 3;
    const contextsPerLevel = Math.ceil(sortedContexts.length / maxLevels);

    for (let i = 0; i < maxLevels && i * contextsPerLevel < sortedContexts.length; i++) {
      const levelContexts = sortedContexts.slice(i * contextsPerLevel, (i + 1) * contextsPerLevel);
      const tokenCount = levelContexts.reduce((sum, ctx) => sum + ctx.compressedTokens, 0);

      levels.push({
        level: i + 1,
        name: i === 0 ? 'Essential' : i === 1 ? 'Important' : 'Supporting',
        description: this.generateLevelDescription(i + 1, levelContexts),
        contexts: levelContexts.map(ctx => ctx.id),
        tokenCount,
        importance: 1 - (i / maxLevels),
        dependencies: i > 0 ? [levels[i - 1].level.toString()] : [],
        loadPriority: maxLevels - i
      });
    }

    return {
      levels,
      totalLevels: levels.length,
      loadingStrategy: 'progressive',
      levelThresholds: levels.map(level => level.tokenCount)
    };
  }

  /**
   * Extract preserved elements
   */
  private extractPreservedElements(contexts: CompressedContextItem[]): PreservedElements {
    const preserved: PreservedElements = {
      imports: [],
      exports: [],
      types: [],
      interfaces: [],
      functions: [],
      classes: [],
      constants: [],
      comments: [],
      documentation: []
    };

    for (const context of contexts) {
      preserved.imports.push(...this.extractImports(context.compressedContent));
      preserved.exports.push(...this.extractExports(context.compressedContent));
      preserved.types.push(...this.extractTypes(context.compressedContent));
      preserved.interfaces.push(...this.extractInterfaces(context.compressedContent));
      preserved.functions.push(...this.extractFunctions(context.compressedContent));
      preserved.classes.push(...this.extractClasses(context.compressedContent));
      preserved.constants.push(...this.extractConstants(context.compressedContent));

      if (context.preservedElements.includes('comments')) {
        preserved.comments.push(...this.extractComments(context.compressedContent));
      }
    }

    // Deduplicate
    Object.keys(preserved).forEach(key => {
      preserved[key as keyof PreservedElements] = [...new Set(preserved[key as keyof PreservedElements])];
    });

    return preserved;
  }

  // Final utility methods

  private calculateCompressionMetrics(
    originalContexts: ContextItem[],
    compressedContexts: CompressedContextItem[],
    startTime: number
  ): CompressionMetrics {
    const totalOriginalTokens = originalContexts.reduce((sum, ctx) => sum + ctx.tokens, 0);
    const totalCompressedTokens = compressedContexts.reduce((sum, ctx) => sum + ctx.compressedTokens, 0);
    const overallCompressionRatio = totalCompressedTokens / totalOriginalTokens;
    const compressionTime = Date.now() - startTime;

    // Calculate quality score (simplified)
    const qualityScore = Math.max(0, Math.min(1, 1 - (1 - overallCompressionRatio) * 0.5));
    const informationLoss = 1 - qualityScore;

    // Aggregate technique effectiveness
    const techniques: CompressionMetrics['techniques'] = {};
    for (const context of compressedContexts) {
      for (const technique of context.compressionTechniques) {
        if (!techniques[technique]) {
          techniques[technique] = { applied: 0, tokensReduced: 0, effectiveness: 0 };
        }
        techniques[technique].applied++;
        techniques[technique].tokensReduced += context.originalTokens - context.compressedTokens;
      }
    }

    // Calculate effectiveness for each technique
    Object.keys(techniques).forEach(technique => {
      const data = techniques[technique];
      data.effectiveness = data.tokensReduced / (data.applied * 100); // Normalized effectiveness
    });

    return {
      totalOriginalTokens,
      totalCompressedTokens,
      overallCompressionRatio,
      compressionTime,
      qualityScore,
      informationLoss,
      techniques
    };
  }

  private updateCompressionStats(metrics: CompressionMetrics): void {
    this.stats.totalCompressions++;
    this.stats.averageCompressionRatio =
      (this.stats.averageCompressionRatio * (this.stats.totalCompressions - 1) + metrics.overallCompressionRatio) / this.stats.totalCompressions;
    this.stats.averageQualityScore =
      (this.stats.averageQualityScore * (this.stats.totalCompressions - 1) + metrics.qualityScore) / this.stats.totalCompressions;
    this.stats.averageProcessingTime =
      (this.stats.averageProcessingTime * (this.stats.totalCompressions - 1) + metrics.compressionTime) / this.stats.totalCompressions;
    this.stats.totalTokensReduced += metrics.totalOriginalTokens - metrics.totalCompressedTokens;

    // Update technique effectiveness
    Object.keys(metrics.techniques).forEach(technique => {
      if (!this.stats.techniqueEffectiveness[technique]) {
        this.stats.techniqueEffectiveness[technique] = { usageCount: 0, averageReduction: 0, averageQuality: 0 };
      }
      const stats = this.stats.techniqueEffectiveness[technique];
      const newData = metrics.techniques[technique];

      stats.usageCount += newData.applied;
      stats.averageReduction = (stats.averageReduction + newData.tokensReduced) / 2;
      stats.averageQuality = (stats.averageQuality + newData.effectiveness) / 2;
    });
  }

  // Helper methods for various calculations and extractions

  private validateCompressionRequest(request: CompressionRequest): void {
    if (request.targetTokens <= 0) {
      throw new Error('Target tokens must be positive');
    }
    if (request.maxTokens < request.targetTokens) {
      throw new Error('Max tokens must be >= target tokens');
    }
    if (request.contexts.length === 0) {
      throw new Error('No contexts provided for compression');
    }
  }

  private calculateTotalTokens(contexts: ContextItem[]): number {
    return contexts.reduce((sum, ctx) => sum + ctx.tokens, 0);
  }

  private createUncompressedResult(request: CompressionRequest, originalTokens: number): CompressedContext {
    const compressedContexts: CompressedContextItem[] = request.contexts.map(ctx => ({
      id: this.generateCompressedContextId(),
      originalId: ctx.id,
      type: ctx.type,
      source: ctx.source,
      compressedContent: ctx.content,
      originalTokens: ctx.tokens,
      compressedTokens: ctx.tokens,
      compressionRatio: 1.0,
      relevanceScore: ctx.relevanceScore,
      preservedElements: ['all'],
      compressionTechniques: [],
      metadata: {
        language: ctx.language,
        symbols: ctx.metadata.symbols,
        keyElements: [],
        relationships: [],
        importance: ctx.relevanceScore
      }
    }));

    return {
      id: this.generateResultId(),
      requestId: request.id,
      originalTokens,
      compressedTokens: originalTokens,
      compressionRatio: 1.0,
      contexts: compressedContexts,
      summary: {
        overview: 'No compression applied - content fits within target',
        keyPoints: [],
        mainConcepts: [],
        criticalElements: [],
        relationships: [],
        dependencies: [],
        patterns: [],
        recommendations: [],
        tokenCount: originalTokens
      },
      hierarchy: {
        levels: [{
          level: 1,
          name: 'All Content',
          description: 'All content preserved without compression',
          contexts: compressedContexts.map(ctx => ctx.id),
          tokenCount: originalTokens,
          importance: 1.0,
          dependencies: [],
          loadPriority: 1
        }],
        totalLevels: 1,
        loadingStrategy: 'progressive',
        levelThresholds: [originalTokens]
      },
      preservedElements: this.extractPreservedElements(compressedContexts),
      compressionMetrics: {
        totalOriginalTokens: originalTokens,
        totalCompressedTokens: originalTokens,
        overallCompressionRatio: 1.0,
        compressionTime: 0,
        qualityScore: 1.0,
        informationLoss: 0,
        techniques: {}
      },
      generatedAt: Date.now(),
      expiresAt: Date.now() + (60 * 60 * 1000)
    };
  }

  private estimateTokenCount(content: string): number {
    // Simple token estimation (roughly 4 characters per token)
    return Math.ceil(content.length / 4);
  }

  private generateCacheKey(request: CompressionRequest): string {
    const contextIds = request.contexts.map(ctx => ctx.id).sort().join(',');
    return `${contextIds}-${request.targetTokens}-${request.compressionLevel}`;
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateResultId(): string {
    return `result-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCompressedContextId(): string {
    return `compressed-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Simplified implementations for various extraction methods
  private extractKeyElements(content: string): string[] {
    const elements = [];
    const patterns = [
      /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g,
      /interface\s+(\w+)/g,
      /type\s+(\w+)/g
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        elements.push(match[1]);
      }
    }

    return elements;
  }

  private extractRelationships(content: string): string[] {
    const relationships = [];
    if (content.includes('extends')) relationships.push('inheritance');
    if (content.includes('implements')) relationships.push('implementation');
    if (content.includes('import')) relationships.push('dependency');
    if (content.includes('export')) relationships.push('provides');
    return relationships;
  }

  private extractImports(content: string): string[] {
    const imports = content.match(/import\s+.*?from\s+['"][^'"]+['"]/g) || [];
    return imports;
  }

  private extractExports(content: string): string[] {
    const exports = content.match(/export\s+(?:default\s+)?.*?(?:;|$)/gm) || [];
    return exports;
  }

  private extractTypes(content: string): string[] {
    const types = content.match(/type\s+\w+/g) || [];
    return types;
  }

  private extractInterfaces(content: string): string[] {
    const interfaces = content.match(/interface\s+\w+/g) || [];
    return interfaces;
  }

  private extractFunctions(content: string): string[] {
    const functions = content.match(/(?:function\s+\w+|const\s+\w+\s*=\s*(?:\([^)]*\)\s*=>|\w+))/g) || [];
    return functions;
  }

  private extractClasses(content: string): string[] {
    const classes = content.match(/class\s+\w+/g) || [];
    return classes;
  }

  private extractConstants(content: string): string[] {
    const constants = content.match(/const\s+[A-Z_][A-Z0-9_]*\s*=/g) || [];
    return constants;
  }

  private extractComments(content: string): string[] {
    const comments = content.match(/\/\*[\s\S]*?\*\/|\/\/.*$/gm) || [];
    return comments;
  }

  // Additional helper methods with simplified implementations
  private calculateRecencyScore(context: ContextItem): number {
    return context.metadata.lastModified ? Math.max(0, 1 - (Date.now() - context.metadata.lastModified) / (24 * 60 * 60 * 1000)) : 0.5;
  }

  private calculateImportanceScore(context: ContextItem): number {
    return context.metadata.importance || context.relevanceScore;
  }

  private calculateDependencyScore(context: ContextItem): number {
    return context.metadata.dependencies ? Math.min(1, context.metadata.dependencies.length / 10) : 0;
  }

  private async groupContextsBySemantic(contexts: ContextItem[]): Promise<ContextItem[][]> {
    // Simplified semantic grouping
    const groups: ContextItem[][] = [];
    const processed = new Set<string>();

    for (const context of contexts) {
      if (processed.has(context.id)) continue;

      const group = [context];
      processed.add(context.id);

      // Find similar contexts (simplified)
      for (const other of contexts) {
        if (processed.has(other.id)) continue;
        if (this.areContextsSimilar(context, other)) {
          group.push(other);
          processed.add(other.id);
        }
      }

      groups.push(group);
    }

    return groups;
  }

  private areContextsSimilar(ctx1: ContextItem, ctx2: ContextItem): boolean {
    // Simplified similarity check
    return ctx1.type === ctx2.type &&
           ctx1.language === ctx2.language &&
           Math.abs(ctx1.relevanceScore - ctx2.relevanceScore) < 0.2;
  }

  private selectRepresentativeContext(group: ContextItem[]): ContextItem {
    return group.reduce((best, current) =>
      current.relevanceScore > best.relevanceScore ? current : best
    );
  }

  private scoreOptimizationResult(contexts: ContextItem[]): number {
    return contexts.reduce((sum, ctx) => sum + ctx.relevanceScore, 0) / contexts.length;
  }

  private containsCriticalElements(content: string): boolean {
    return this.config.preservationRules.criticalElements.some(element =>
      content.toLowerCase().includes(element.toLowerCase())
    );
  }

  private generateShortVariableName(original: string, counter: number): string {
    const prefix = original.charAt(0).toLowerCase();
    return `${prefix}${counter}`;
  }

  private createCodeSummary(code: string): string {
    const lines = code.split('\n').length;
    const functions = (code.match(/function|=>/g) || []).length;
    const conditions = (code.match(/if|while|for/g) || []).length;
    return `${lines} lines, ${functions} functions, ${conditions} conditions`;
  }

  private extractLineConcept(line: string): string | null {
    const trimmed = line.trim();
    if (trimmed.startsWith('//') || trimmed.startsWith('/*')) return 'comment';
    if (trimmed.includes('function') || trimmed.includes('=>')) return 'function';
    if (trimmed.includes('class')) return 'class';
    if (trimmed.includes('import')) return 'import';
    if (trimmed.includes('export')) return 'export';
    return null;
  }

  private isImportUsed(importedItems: string, content: string): boolean {
    // Simplified usage check
    if (importedItems.includes('{')) {
      const items = importedItems.replace(/[{}]/g, '').split(',').map(item => item.trim());
      return items.some(item => content.includes(item));
    } else {
      return content.includes(importedItems.trim());
    }
  }

  private generateOverview(contexts: CompressedContextItem[]): string {
    const totalContexts = contexts.length;
    const totalTokens = contexts.reduce((sum, ctx) => sum + ctx.compressedTokens, 0);
    const avgCompression = contexts.reduce((sum, ctx) => sum + ctx.compressionRatio, 0) / totalContexts;

    return `Compressed ${totalContexts} contexts to ${totalTokens} tokens with ${(avgCompression * 100).toFixed(1)}% average compression`;
  }

  private extractKeyPoints(contexts: CompressedContextItem[]): string[] {
    const points = [];
    for (const context of contexts) {
      if (context.metadata.keyElements) {
        points.push(...context.metadata.keyElements);
      }
    }
    return [...new Set(points)].slice(0, 10);
  }

  private extractMainConcepts(contexts: CompressedContextItem[]): string[] {
    const concepts = new Set<string>();
    for (const context of contexts) {
      if (context.type) concepts.add(context.type);
      if (context.metadata.language) concepts.add(context.metadata.language);
    }
    return Array.from(concepts);
  }

  private extractCriticalElements(contexts: CompressedContextItem[]): string[] {
    const critical = [];
    for (const context of contexts) {
      if (context.relevanceScore > 0.8) {
        critical.push(context.source);
      }
    }
    return critical;
  }

  private extractDependencies(contexts: CompressedContextItem[]): string[] {
    const deps = new Set<string>();
    for (const context of contexts) {
      const imports = this.extractImports(context.compressedContent);
      imports.forEach(imp => {
        const match = imp.match(/from\s+['"]([^'"]+)['"]/);
        if (match) deps.add(match[1]);
      });
    }
    return Array.from(deps);
  }

  private extractPatterns(contexts: CompressedContextItem[]): string[] {
    const patterns = new Set<string>();
    for (const context of contexts) {
      context.compressionTechniques.forEach(technique => patterns.add(technique));
    }
    return Array.from(patterns);
  }

  private generateRecommendations(contexts: CompressedContextItem[]): string[] {
    const recommendations = [];
    const avgCompression = contexts.reduce((sum, ctx) => sum + ctx.compressionRatio, 0) / contexts.length;

    if (avgCompression > 0.8) {
      recommendations.push('Consider more aggressive compression for better token efficiency');
    }
    if (avgCompression < 0.3) {
      recommendations.push('Compression may be too aggressive, consider preserving more content');
    }

    return recommendations;
  }

  private generateLevelDescription(level: number, contexts: any[]): string {
    const contextTypes = contexts.map(ctx => ctx.type || 'unknown');
    const uniqueTypes = [...new Set(contextTypes)];
    return `Level ${level} contains ${contexts.length} contexts of types: ${uniqueTypes.join(', ')}`;
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('contextCompression.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load context compression configuration:', error);
    }
  }

  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('contextCompression.config', this.config);
    } catch (error) {
      console.error('Failed to save context compression configuration:', error);
    }
  }

  /**
   * Shutdown the context compression system
   */
  async shutdown(): Promise<void> {
    try {
      // Clear caches
      this.compressionCache.clear();
      this.activeCompressions.clear();
      this.processingQueue = [];

      console.log('Context compression system shut down');
    } catch (error) {
      console.error('Error during context compression shutdown:', error);
    }
  }
}

// Global instances per project
const globalContextCompression: Map<string, ContextCompression> = new Map();

/**
 * Get the context compression instance for a project
 */
export function getContextCompression(projectId: string): ContextCompression {
  if (!globalContextCompression.has(projectId)) {
    globalContextCompression.set(projectId, new ContextCompression(projectId));
  }
  return globalContextCompression.get(projectId)!;
}