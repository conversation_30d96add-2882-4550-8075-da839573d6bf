// components/agents/model-metadata-test.tsx
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UniversalModelSelector } from './universal-model-selector';
import { ModelPricingFooter } from './model-pricing-footer';
import { 
  MODEL_METADATA, 
  getModelMetadata, 
  getModelsByProvider, 
  getModelsByTag,
  formatContextSize,
  formatPricing,
  isModelDeprecated
} from './model-metadata';
import { LLMProvider, getAllProviders, getProviderModels } from './llm-provider-registry';

export const ModelMetadataTest: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>('openai');
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testMetadataFunctions = () => {
    addResult('🧪 Testing model metadata functions...');
    
    // Test total metadata count
    addResult(`✅ Total models in metadata: ${MODEL_METADATA.length}`);
    
    // Test provider breakdown
    const providers = getAllProviders();
    providers.forEach(provider => {
      const models = getModelsByProvider(provider);
      addResult(`✅ ${provider}: ${models.length} models`);
    });
    
    // Test tag filtering
    const fastModels = getModelsByTag('fast');
    const costEffectiveModels = getModelsByTag('cost-effective');
    const multimodalModels = getModelsByTag('multimodal');
    addResult(`✅ Fast models: ${fastModels.length}`);
    addResult(`✅ Cost-effective models: ${costEffectiveModels.length}`);
    addResult(`✅ Multimodal models: ${multimodalModels.length}`);
    
    // Test specific model lookup
    const gpt4oMeta = getModelMetadata('openai', 'gpt-4o');
    const claudeOpus4Meta = getModelMetadata('anthropic', 'claude-opus-4-20250514');
    addResult(`✅ GPT-4o metadata: ${gpt4oMeta ? 'Found' : 'Missing'}`);
    addResult(`✅ Claude Opus 4 metadata: ${claudeOpus4Meta ? 'Found' : 'Missing'}`);
    
    // Test deprecation status
    const deprecatedCount = MODEL_METADATA.filter(m => m.deprecated).length;
    addResult(`✅ Deprecated models: ${deprecatedCount}`);
  };

  const testPricingData = () => {
    addResult('💰 Testing pricing data...');
    
    // Test pricing ranges
    const allPricing = MODEL_METADATA.map(m => m.pricing);
    const inputPrices = allPricing.map(p => p.input);
    const outputPrices = allPricing.map(p => p.output);
    
    const minInput = Math.min(...inputPrices);
    const maxInput = Math.max(...inputPrices);
    const minOutput = Math.min(...outputPrices);
    const maxOutput = Math.max(...outputPrices);
    
    addResult(`✅ Input pricing range: $${minInput.toFixed(4)} - $${maxInput.toFixed(4)}`);
    addResult(`✅ Output pricing range: $${minOutput.toFixed(4)} - $${maxOutput.toFixed(4)}`);
    
    // Test most/least expensive models
    const sortedByInput = MODEL_METADATA.sort((a, b) => a.pricing.input - b.pricing.input);
    const cheapestInput = sortedByInput[0];
    const expensiveInput = sortedByInput[sortedByInput.length - 1];
    
    addResult(`✅ Cheapest input: ${cheapestInput.label} ($${cheapestInput.pricing.input.toFixed(4)})`);
    addResult(`✅ Most expensive input: ${expensiveInput.label} ($${expensiveInput.pricing.input.toFixed(4)})`);
  };

  const testContextSizes = () => {
    addResult('📏 Testing context sizes...');
    
    // Test context size ranges
    const contextSizes = MODEL_METADATA.map(m => m.contextSize);
    const minContext = Math.min(...contextSizes);
    const maxContext = Math.max(...contextSizes);
    
    addResult(`✅ Context range: ${formatContextSize(minContext)} - ${formatContextSize(maxContext)}`);
    
    // Test ultra-long context models
    const longContextModels = MODEL_METADATA.filter(m => m.contextSize >= 1_000_000);
    addResult(`✅ Ultra-long context models (1M+): ${longContextModels.length}`);
    longContextModels.forEach(m => {
      addResult(`   - ${m.label}: ${formatContextSize(m.contextSize)}`);
    });
    
    // Test standard context models
    const standardContextModels = MODEL_METADATA.filter(m => m.contextSize <= 32_768);
    addResult(`✅ Standard context models (≤32K): ${standardContextModels.length}`);
  };

  const testTagCoverage = () => {
    addResult('🏷️ Testing tag coverage...');
    
    // Collect all unique tags
    const allTags = new Set<string>();
    MODEL_METADATA.forEach(model => {
      model.tags.forEach(tag => allTags.add(tag));
    });
    
    addResult(`✅ Total unique tags: ${allTags.size}`);
    
    // Test tag frequency
    const tagCounts: Record<string, number> = {};
    MODEL_METADATA.forEach(model => {
      model.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    
    const sortedTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
    
    addResult(`✅ Top 5 tags:`);
    sortedTags.forEach(([tag, count]) => {
      addResult(`   - ${tag}: ${count} models`);
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const selectedMetadata = getModelMetadata(selectedProvider, selectedModel);
  const availableModels = getProviderModels(selectedProvider);

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle>Model Metadata Test Suite</CardTitle>
        <CardDescription>
          Test the comprehensive model metadata system with pricing, context, and capability information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Model Selection Test */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Model Selection Test</h3>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Provider</label>
              <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as LLMProvider)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getAllProviders().map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {provider.charAt(0).toUpperCase() + provider.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <UniversalModelSelector
              provider={selectedProvider}
              value={selectedModel}
              onChange={setSelectedModel}
              availableModels={availableModels}
              placeholder={`Select a ${selectedProvider} model`}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Metadata Statistics</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">{MODEL_METADATA.length}</div>
                <div className="text-sm text-muted-foreground">Total Models</div>
              </div>
              <div className="p-3 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">{getAllProviders().length}</div>
                <div className="text-sm text-muted-foreground">Providers</div>
              </div>
              <div className="p-3 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">{getModelsByTag('cost-effective').length}</div>
                <div className="text-sm text-muted-foreground">Cost-Effective</div>
              </div>
              <div className="p-3 bg-muted rounded-lg text-center">
                <div className="text-2xl font-bold">{getModelsByTag('multimodal').length}</div>
                <div className="text-sm text-muted-foreground">Multimodal</div>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Footer Test */}
        {selectedMetadata && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Pricing Footer Test</h3>
            <ModelPricingFooter
              provider={selectedProvider}
              modelId={selectedModel}
              className="border rounded-lg p-4"
            />
          </div>
        )}

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={testMetadataFunctions} variant="outline">
            Test Metadata Functions
          </Button>
          <Button onClick={testPricingData} variant="outline">
            Test Pricing Data
          </Button>
          <Button onClick={testContextSizes} variant="outline">
            Test Context Sizes
          </Button>
          <Button onClick={testTagCoverage} variant="outline">
            Test Tag Coverage
          </Button>
          <Button onClick={clearResults} variant="secondary">
            Clear Results
          </Button>
        </div>

        {/* Provider Breakdown */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Provider Model Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getAllProviders().map((provider) => {
              const models = getModelsByProvider(provider);
              return (
                <Card key={provider}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      {provider.charAt(0).toUpperCase() + provider.slice(1)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold mb-2">{models.length}</div>
                    <div className="text-sm text-muted-foreground mb-2">models available</div>
                    <div className="flex flex-wrap gap-1">
                      {models.slice(0, 3).map((model) => (
                        <Badge key={model.modelId} variant="outline" className="text-xs">
                          {model.label.split(' ').slice(0, 2).join(' ')}
                        </Badge>
                      ))}
                      {models.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{models.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-2">
          <h4 className="font-semibold">Test Results</h4>
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index} className="whitespace-pre-wrap">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ModelMetadataTest;
