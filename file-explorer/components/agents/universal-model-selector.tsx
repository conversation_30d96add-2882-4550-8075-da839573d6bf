// components/agents/universal-model-selector.tsx
import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, DollarSign, Hash, Info } from 'lucide-react';
import { 
  getModelMetadata, 
  getModelsByProvider, 
  formatContextSize, 
  formatPricing,
  isModelDeprecated,
  ModelMetadata 
} from './model-metadata';
import { LLMProvider } from './llm-provider-registry';

interface UniversalModelSelectorProps {
  provider: LLMProvider;
  value: string;
  onChange: (value: string) => void;
  availableModels: string[];
  showCustomInput?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const UniversalModelSelector: React.FC<UniversalModelSelectorProps> = ({
  provider,
  value,
  onChange,
  availableModels,
  showCustomInput = true,
  disabled = false,
  placeholder = "Select a model"
}) => {
  const [showCustom, setShowCustom] = useState(false);
  const [customModel, setCustomModel] = useState('');

  // Get metadata for the selected model
  const selectedMetadata = getModelMetadata(provider, value);
  
  // Get all known models for this provider from metadata
  const knownModels = getModelsByProvider(provider);
  
  // Combine available models with known metadata
  const enrichedModels = availableModels.map(modelId => {
    const metadata = getModelMetadata(provider, modelId);
    return {
      id: modelId,
      label: metadata?.label || modelId,
      metadata
    };
  });

  // Group models by category if we have metadata
  const groupedModels = enrichedModels.reduce((groups, model) => {
    if (!model.metadata) {
      if (!groups['Other']) groups['Other'] = [];
      groups['Other'].push(model);
      return groups;
    }

    // Determine group based on tags or model name
    let group = 'General';
    if (model.metadata.tags.includes('cost-effective') || model.metadata.tags.includes('fast')) {
      group = 'Fast & Affordable';
    } else if (model.metadata.tags.includes('advanced reasoning') || model.metadata.tags.includes('complex analysis')) {
      group = 'Advanced';
    } else if (model.metadata.tags.includes('code generation') || model.metadata.tags.includes('programming')) {
      group = 'Code Specialized';
    } else if (model.metadata.tags.includes('multimodal') || model.metadata.tags.includes('vision')) {
      group = 'Multimodal';
    }

    if (!groups[group]) groups[group] = [];
    groups[group].push(model);
    return groups;
  }, {} as Record<string, typeof enrichedModels>);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setShowCustom(true);
    } else {
      setShowCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomSubmit = () => {
    if (customModel.trim()) {
      onChange(customModel.trim());
      setShowCustom(false);
      setCustomModel('');
    }
  };

  const handleCustomCancel = () => {
    setShowCustom(false);
    setCustomModel('');
  };

  const getModelBadgeVariant = (metadata?: ModelMetadata) => {
    if (!metadata) return 'outline';
    if (metadata.deprecated) return 'destructive';
    if (metadata.tags.includes('cost-effective')) return 'secondary';
    if (metadata.tags.includes('advanced reasoning')) return 'default';
    return 'outline';
  };

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label>Model</Label>
        
        {!showCustom ? (
          <Select
            value={value}
            onValueChange={handleSelectChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(groupedModels).map(([groupName, models]) => (
                <div key={groupName}>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
                    {groupName}
                  </div>
                  {models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center gap-2 w-full">
                        <span className="flex-1">{model.label}</span>
                        {model.metadata && (
                          <div className="flex items-center gap-1">
                            {model.metadata.deprecated && (
                              <Badge variant="destructive" className="text-xs">Deprecated</Badge>
                            )}
                            {model.metadata.tags.includes('cost-effective') && (
                              <Badge variant="secondary" className="text-xs">$</Badge>
                            )}
                            {model.metadata.tags.includes('fast') && (
                              <Badge variant="outline" className="text-xs">Fast</Badge>
                            )}
                            {model.metadata.tags.includes('multimodal') && (
                              <Badge variant="outline" className="text-xs">Vision</Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </div>
              ))}

              {showCustomInput && (
                <>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                    Custom Model
                  </div>
                  <SelectItem value="custom">
                    <div className="flex items-center gap-2">
                      <span>Custom Model ID...</span>
                      <Badge variant="outline" className="text-xs">Manual</Badge>
                    </div>
                  </SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        ) : (
          <div className="space-y-2">
            <Input
              placeholder={`Enter custom ${provider} model ID`}
              value={customModel}
              onChange={(e) => setCustomModel(e.target.value)}
              disabled={disabled}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleCustomSubmit} disabled={!customModel.trim()}>
                Use Model
              </Button>
              <Button size="sm" variant="outline" onClick={handleCustomCancel}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Model Information Card */}
      {selectedMetadata && !showCustom && (
        <Card>
          <CardContent className="p-4 space-y-3">
            <div className="flex items-center gap-2">
              <Badge variant={getModelBadgeVariant(selectedMetadata)}>
                {provider.charAt(0).toUpperCase() + provider.slice(1)}
              </Badge>
              <span className="font-medium">{selectedMetadata.label}</span>
              {selectedMetadata.deprecated && (
                <Badge variant="destructive" className="text-xs">Deprecated</Badge>
              )}
            </div>
            
            {selectedMetadata.notes && (
              <p className="text-sm text-muted-foreground">
                {selectedMetadata.notes}
              </p>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Context:</span>
                <span>{formatContextSize(selectedMetadata.contextSize)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Pricing:</span>
                <span className="text-xs">{formatPricing(selectedMetadata.pricing)}</span>
              </div>
              
              {selectedMetadata.releaseDate && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Released:</span>
                  <span>{selectedMetadata.releaseDate}</span>
                </div>
              )}
            </div>
            
            {selectedMetadata.tags.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">Capabilities:</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {selectedMetadata.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Custom Model Info */}
      {!selectedMetadata && value && !showCustom && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Badge variant="outline">Custom</Badge>
              <span className="font-medium">{value}</span>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Custom {provider} model - metadata not available
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UniversalModelSelector;
