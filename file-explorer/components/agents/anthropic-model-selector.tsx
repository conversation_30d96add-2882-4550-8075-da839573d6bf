// components/agents/anthropic-model-selector.tsx
import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ANTHROPIC_MODELS, getAnthropicModelById, getAnthropicModelOptions } from './anthropic-models';

interface AnthropicModelSelectorProps {
  value: string;
  onChange: (value: string) => void;
  showCustomInput?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const AnthropicModelSelector: React.FC<AnthropicModelSelectorProps> = ({
  value,
  onChange,
  showCustomInput = true,
  disabled = false,
  placeholder = "Select an Anthropic model"
}) => {
  const [showCustom, setShowCustom] = useState(false);
  const [customModel, setCustomModel] = useState('');

  const modelOptions = getAnthropicModelOptions();
  const selectedModel = getAnthropicModelById(value);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setShowCustom(true);
    } else {
      setShowCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomSubmit = () => {
    if (customModel.trim()) {
      onChange(customModel.trim());
      setShowCustom(false);
      setCustomModel('');
    }
  };

  const handleCustomCancel = () => {
    setShowCustom(false);
    setCustomModel('');
  };

  const getModelBadgeVariant = (modelId: string) => {
    if (modelId.includes('claude-opus-4') || modelId.includes('claude-sonnet-4')) {
      return 'default'; // Claude 4 series
    }
    if (modelId.includes('claude-3-7')) {
      return 'secondary'; // Claude 3.7 series
    }
    if (modelId.includes('claude-3-5')) {
      return 'outline'; // Claude 3.5 series
    }
    return 'secondary'; // Claude 3 series
  };

  const getModelSeries = (modelId: string) => {
    if (modelId.includes('claude-opus-4') || modelId.includes('claude-sonnet-4')) {
      return 'Claude 4';
    }
    if (modelId.includes('claude-3-7')) {
      return 'Claude 3.7';
    }
    if (modelId.includes('claude-3-5')) {
      return 'Claude 3.5';
    }
    if (modelId.includes('claude-3')) {
      return 'Claude 3';
    }
    return 'Claude';
  };

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label>Anthropic Model</Label>
        
        {!showCustom ? (
          <Select
            value={value}
            onValueChange={handleSelectChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {/* Group by model series */}
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
                Claude 4 Series (Latest)
              </div>
              {modelOptions
                .filter(option => option.value.includes('claude-opus-4') || option.value.includes('claude-sonnet-4'))
                .map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.label}</span>
                      <Badge variant="default" className="text-xs">New</Badge>
                    </div>
                  </SelectItem>
                ))}

              <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                Claude 3.7 Series (Enhanced)
              </div>
              {modelOptions
                .filter(option => option.value.includes('claude-3-7'))
                .map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.label}</span>
                      <Badge variant="secondary" className="text-xs">Enhanced</Badge>
                    </div>
                  </SelectItem>
                ))}

              <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                Claude 3.5 Series (Current)
              </div>
              {modelOptions
                .filter(option => option.value.includes('claude-3-5'))
                .map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.label}</span>
                      {option.value.includes('20241022') && (
                        <Badge variant="outline" className="text-xs">Latest</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}

              <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                Claude 3 Series (Stable)
              </div>
              {modelOptions
                .filter(option => option.value.includes('claude-3-') && !option.value.includes('claude-3-5') && !option.value.includes('claude-3-7'))
                .map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}

              {showCustomInput && (
                <>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                    Custom Model
                  </div>
                  <SelectItem value="custom">
                    <div className="flex items-center gap-2">
                      <span>Custom Model ID...</span>
                      <Badge variant="outline" className="text-xs">Manual</Badge>
                    </div>
                  </SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        ) : (
          <div className="space-y-2">
            <Input
              placeholder="Enter custom Anthropic model ID (e.g., claude-3-opus-20240229)"
              value={customModel}
              onChange={(e) => setCustomModel(e.target.value)}
              disabled={disabled}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleCustomSubmit} disabled={!customModel.trim()}>
                Use Model
              </Button>
              <Button size="sm" variant="outline" onClick={handleCustomCancel}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Model Information */}
      {selectedModel && !showCustom && (
        <div className="p-3 bg-muted rounded-lg space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant={getModelBadgeVariant(selectedModel.id)}>
              {getModelSeries(selectedModel.id)}
            </Badge>
            <span className="text-sm font-medium">{selectedModel.label}</span>
          </div>
          
          {selectedModel.description && (
            <p className="text-sm text-muted-foreground">
              {selectedModel.description}
            </p>
          )}
          
          <div className="grid grid-cols-2 gap-4 text-xs">
            {selectedModel.contextLength && (
              <div>
                <span className="font-medium">Context:</span> {selectedModel.contextLength.toLocaleString()} tokens
              </div>
            )}
            {selectedModel.costPer1kTokens && (
              <div>
                <span className="font-medium">Cost:</span> ${selectedModel.costPer1kTokens.input}/${selectedModel.costPer1kTokens.output} per 1K tokens
              </div>
            )}
          </div>
          
          {selectedModel.capabilities && selectedModel.capabilities.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedModel.capabilities.map((capability) => (
                <Badge key={capability} variant="outline" className="text-xs">
                  {capability.replace(/_/g, ' ')}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Custom Model Info */}
      {!selectedModel && value && !showCustom && (
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Custom</Badge>
            <span className="text-sm font-medium">{value}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Custom Anthropic model ID
          </p>
        </div>
      )}
    </div>
  );
};

export default AnthropicModelSelector;
