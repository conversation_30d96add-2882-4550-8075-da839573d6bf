import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { EditorStateProvider } from "@/components/editor/editor-state-provider"
import { EditorActionProvider } from "@/components/editor/editor-action-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "CodeFusion - Modern Code Editor",
  description: "A modern code editor with AI capabilities",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <EditorStateProvider>
            <EditorActionProvider>
              {children}
            </EditorActionProvider>
          </EditorStateProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
